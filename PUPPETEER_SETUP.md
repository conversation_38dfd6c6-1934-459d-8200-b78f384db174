# Puppeteer PDF生成服务设置指南

## 🎯 概述

我们已经将PDF生成从前端html2pdf方案升级为基于Puppeteer的后端渲染方案，这将提供：

- ✅ 更可靠的PDF生成
- ✅ 更好的页面渲染质量
- ✅ 完美的多页面支持
- ✅ 服务器端渲染，避免浏览器兼容性问题

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）

```bash
# 运行启动脚本
./start-server.sh
```

### 方法二：手动启动

```bash
# 1. 安装依赖
npm install

# 2. 启动Puppeteer服务
npm run server
# 或者
node server.js
```

## 🔧 系统要求

- **Node.js**: 版本 16+ 
- **npm**: 版本 8+
- **内存**: 建议至少 2GB 可用内存
- **操作系统**: macOS, Linux, Windows

## 📋 使用步骤

1. **启动后端服务**
   ```bash
   ./start-server.sh
   ```
   服务将运行在 `http://localhost:3000`

2. **启动前端服务**（在新终端窗口）
   ```bash
   python3 -m http.server 8000
   ```
   前端将运行在 `http://localhost:8000`

3. **使用应用**
   - 打开浏览器访问 `http://localhost:8000`
   - 配置练习题内容和页面数量
   - 点击"生成练习题"
   - 点击"生成PDF"按钮

## 🔍 API端点

### POST /api/generate-pdf
生成PDF文件

**请求体:**
```json
{
  "pages": [
    {
      "html": "<div>页面HTML内容</div>",
      "index": 1
    }
  ],
  "filename": "mathdrills.pdf"
}
```

**响应:** PDF文件流

### POST /api/generate-images
生成图片文件

**请求体:**
```json
{
  "pages": [
    {
      "html": "<div>页面HTML内容</div>",
      "index": 1
    }
  ]
}
```

**响应:**
```json
{
  "images": [
    {
      "index": 1,
      "data": "base64编码的图片数据",
      "filename": "mathdrills_page_1.png"
    }
  ]
}
```

### GET /api/health
健康检查

**响应:**
```json
{
  "status": "ok",
  "timestamp": "2025-07-23T16:30:00.000Z"
}
```

## 🐛 故障排除

### 问题1: 无法连接到PDF生成服务
**症状:** 前端显示"无法连接到PDF生成服务"
**解决方案:**
1. 确保后端服务正在运行：`node server.js`
2. 检查端口3000是否被占用：`lsof -i :3000`
3. 检查防火墙设置

### 问题2: Puppeteer安装失败
**症状:** npm install时Puppeteer下载失败
**解决方案:**
```bash
# 设置Puppeteer镜像
export PUPPETEER_DOWNLOAD_HOST=https://npm.taobao.org/mirrors
npm install puppeteer

# 或者跳过Chromium下载，使用系统Chrome
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
npm install puppeteer
```

### 问题3: PDF生成超时
**症状:** PDF生成过程中超时
**解决方案:**
1. 增加服务器内存
2. 减少同时生成的页面数量
3. 检查页面内容是否过于复杂

### 问题4: 字体显示问题
**症状:** PDF中中文字体显示异常
**解决方案:**
1. 确保系统安装了中文字体
2. 在CSS中指定字体回退：
   ```css
   font-family: 'Arial', 'Microsoft YaHei', 'SimSun', sans-serif;
   ```

## 📊 性能优化

### 内存优化
- 每次PDF生成后会自动关闭浏览器实例
- 建议为服务器分配至少2GB内存

### 速度优化
- 使用`headless: 'new'`模式
- 启用GPU加速（生产环境）
- 缓存常用资源

## 🔒 安全考虑

- 服务默认只监听localhost
- 生产环境建议添加认证
- 限制请求频率和大小
- 定期更新Puppeteer版本

## 📝 开发说明

### 添加新功能
1. 修改`server.js`添加新的API端点
2. 更新前端`index.js`调用新API
3. 测试功能并更新文档

### 调试模式
```bash
# 启用Puppeteer调试
DEBUG=puppeteer:* node server.js

# 启用详细日志
NODE_ENV=development node server.js
```

## 🆘 获取帮助

如果遇到问题：
1. 检查控制台错误信息
2. 查看服务器日志
3. 确认所有依赖正确安装
4. 检查网络连接和端口占用

---

**注意**: 首次运行时Puppeteer会下载Chromium浏览器（约100MB），请确保网络连接稳定。
