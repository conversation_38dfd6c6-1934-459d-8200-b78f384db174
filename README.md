# Dan&Dan 的计算练习生成器

一个简单而实用的数学应用题生成器，专为小学数学练习设计。支持多种经典应用题类型，可自动生成练习并导出为精美的 A4 打印格式。

## 🌟 特性

- 📝 支持多种经典应用题类型：
  - 鸡兔同笼问题
  - 植树问题
  - 追击问题
  - 排水问题
  
- 📄 专业的 A4 纸张布局：
  - 精确的 A4 尺寸（210mm × 297mm）
  - 清晰的页眉设计
  - 合理的内容分区
  - 标准的打印边距

- 🖨️ 完善的打印支持：
  - 一键导出 PDF
  - 实时预览
  - 高质量打印效果

- ⚙️ 灵活的配置选项：
  - 可选择不同题型
  - 支持多区域内容
  - 支持多页面生成（1-10页）
  - 实时内容更新
  - 配置自动保存

## 🚀 快速开始

1. 打开应用页面
2. 在配置区选择需要的题型：
   - 每页最多支持 4 个不同的内容区域
   - 每个区域可以选择不同的题型
3. 选择要生成的页面数量（1-10页）
4. 点击"生成练习题"按钮生成内容
5. 预览生成的练习内容
6. 点击"打印练习题"按钮导出 PDF 或直接打印

## ✨ 新功能：多页面生成

现在支持一次性生成多个页面的练习题：

- **页面数量选择**：可以选择生成 1-10 页练习题
- **内容随机化**：每页的内容都是随机生成的，确保练习的多样性
- **统一配置**：所有页面使用相同的题型配置，保持一致性
- **批量打印**：一次性打印所有页面，每页都是完整的 A4 格式
- **页面标识**：每页标题会显示页码，方便区分

### 使用方法：

1. 在"内容配置"区域选择每个部分的题型
2. 在"页面设置"区域选择要生成的页面数量
3. 点击"生成练习题"按钮
4. 预览所有生成的页面
5. 点击"打印练习题"按钮导出包含所有页面的 PDF

## 🛠️ 技术栈

- 前端：
  - 原生 JavaScript
  - HTML5
  - CSS3 (支持打印媒体查询)
- 依赖库：
  - html2pdf.js (PDF 生成)
- 布局：
  - 响应式设计
  - A4 纸张适配

## 📦 项目结构

```
mathdrills/
├── .wrangler/          # Cloudflare Workers 配置目录
├── contents/           # 题型生成脚本
├── styles/            # 样式文件
│   ├── a4.css        # A4 布局样式
│   └── config.css    # 配置区样式
├── img/              # 图片资源
├── index.html        # 主页面
├── index.js          # 主逻辑
└── .gitignore        # Git 忽略配置
```

## 🔍 本地开发

1. 克隆仓库：
```bash
git clone <repository-url>
```

2. 安装依赖：
```bash
npm install
```

3. 在浏览器中打开 index.html 即可开始使用

## 📝 许可证

MIT License
