const fs = require('fs');
const path = require('path');

// 读取 contents 目录
const contentsDir = path.join(__dirname, 'contents');
const files = fs.readdirSync(contentsDir)
    .filter(file => file.endsWith('.js'))
    .filter(file => file !== 'contentList.js')  // 排除生成的文件
    .map(file => file.replace('.js', ''));

// 生成内容列表
const contentList = `// 此文件由构建脚本自动生成
export const contentList = ${JSON.stringify(files, null, 2)};
`;

// 写入到 contentList.js
fs.writeFileSync(
    path.join(__dirname, 'contents', 'contentList.js'),
    contentList
);

console.log('Content list generated successfully!');