async function fetchProblem() {
    try {
        const response = await fetch('https://math.xiaoclass.com/?problemType=鸡兔同笼');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.text();
        console.log('API返回数据:', data);
        
        if (data && data !== '获取问题失败，请稍后再试。') {
            return data;
        }
    } catch (error) {
        console.error('获取问题失败:', error);
    }
    return '获取问题失败，请稍后再试。';
}

// 导出渲染函数
export default function render(containerId) {
    const html = `
        <div class="word-problem loading">
            <span class="problem-text">正在加载题目...</span>
        </div>
    `;
    
    setTimeout(() => {
        fetchProblem().then(problem => {
            // 使用传入的容器ID来定位正确的容器
            const container = document.getElementById(containerId);
            if (container) {
                const problemContainer = container.querySelector('.word-problem');
                if (problemContainer) {
                    const problemText = problemContainer.querySelector('.problem-text');
                    problemText.textContent = problem;
                    problemContainer.classList.remove('loading');
                }
            }
        });
    }, 0);
    
    return html;
}