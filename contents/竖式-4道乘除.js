// 生成随机数字
function randomNum(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成单个乘除法题
function generateProblem() {
    const isMul = Math.random() > 0.5;
    
    if (isMul) {
        // 两个两位数相乘
        const num1 = randomNum(10, 99);
        const num2 = randomNum(10, 99);
        return `${num1} × ${num2} = ______`;
    } else {
        // 三位数除以一位数（不包含1、2）
        const divisor = randomNum(3, 9);
        const quotient = randomNum(34, 111);
        const dividend = divisor * quotient;  // 确保能整除
        return `${dividend} ÷ ${divisor} = ______`;
    }
}

// 导出渲染函数
export default function render() {
    const problems = Array(4).fill(0).map(() => generateProblem());
    
    const html = `
        <div class="problems-container">
            <div class="problems-row">
                ${problems.map(problem => `
                    <div class="problem-item">${problem}</div>
                `).join('')}
            </div>
            <div class="workspace"></div>
        </div>
        <style>
            .problems-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                margin: 0;
                padding: 0;
            }
            
            .problems-row {
                display: flex;
                justify-content: space-around;
                padding: 0 10px;
                margin: 0;
            }
            
            .problem-item {
                font-size: 16px;
                padding: 0 10px;
                margin: 0;
            }
            
            .workspace {
                flex: 1;
                margin: 0;
                padding: 0;
            }
        </style>
    `;
    
    return html;
}
