// 生成随机数字
function randomNum(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成单个加减法题
function generateProblem() {
    const isAdd = Math.random() > 0.5;
    
    if (isAdd) {
        const num1 = randomNum(10, 99);
        const num2 = randomNum(10, 99);
        return `${num1} + ${num2} = ______`;
    } else {
        const larger = randomNum(50, 99);
        const smaller = randomNum(10, 49);
        return `${larger} - ${smaller} = ______`;
    }
}

// 导出渲染函数
export default function render() {
    const problems = Array(4).fill(0).map(() => generateProblem());
    
    const html = `
        <div class="problems-container">
            <div class="problems-row">
                ${problems.map(problem => `
                    <div class="problem-item">${problem}</div>
                `).join('')}
            </div>
            <div class="workspace"></div>
        </div>
        <style>
            .problems-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                margin: 0;
                padding: 0;
            }
            
            .problems-row {
                display: flex;
                justify-content: space-around;
                padding: 0 10px;
                margin: 0;
            }
            
            .problem-item {
                font-size: 16px;
                padding: 0 10px;
                margin: 0;
            }
            
            .workspace {
                flex: 1;
                margin: 0;
                padding: 0;
            }
        </style>
    `;
    
    return html;
}
