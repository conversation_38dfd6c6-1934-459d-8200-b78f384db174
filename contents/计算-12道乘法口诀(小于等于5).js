// 生成随机数字
function randomNum(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成单个乘法口诀题目
function generateProblem() {
    const num1 = randomNum(1, 5); // 乘数 (1-5)
    const num2 = randomNum(1, 5); // 乘数 (1-5)
    return `${num1} × ${num2} = ______`;
}

// 导出渲染函数
export default function render() {
    const problems = Array(12).fill(0).map(() => generateProblem());
    
    // 将题目分成 3 行 4 列
    let rows = [];
    for (let i = 0; i < 3; i++) {
        let row = problems.slice(i * 4, (i + 1) * 4);
        rows.push(row);
    }
    
    const html = `
        <div class="problems-container">
            ${rows.map(row => `
                <div class="problems-row">
                    ${row.map(problem => `
                        <div class="problem-item">
                            ${problem}
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
        <style>
            .problems-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-around; /* 调整间距 */
                padding: 5px;
            }
            
            .problems-row {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
            }
            
            .problem-item {
                font-size: 14px;
                line-height: 1.8;
                white-space: nowrap;
                padding: 0 5px;
            }
        </style>
    `;
    
    return html;
}
