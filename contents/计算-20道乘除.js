// 生成随机数字
function randomNum(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成单个乘除法题
function generateProblem() {
    const isMul = Math.random() > 0.5;  // 50% 概率乘法，50% 概率除法
    
    if (isMul) {  // 乘法
        const num1 = randomNum(2, 9);
        const num2 = randomNum(2, 9);
        return `${num1} × ${num2} = ______`;
    } else {  // 除法
        const num2 = randomNum(2, 9);
        const result = randomNum(2, 9);
        const num1 = num2 * result;  // 确保能整除
        return `${num1} ÷ ${num2} = ______`;
    }
}

// 导出渲染函数
export default function render() {
    const problems = Array(20).fill(0).map(() => generateProblem());
    
    // 将题目分成 5 行 4 列
    let rows = [];
    for (let i = 0; i < 5; i++) {
        let row = problems.slice(i * 4, (i + 1) * 4);
        rows.push(row);
    }
    
    const html = `
        <div class="problems-container">
            ${rows.map(row => `
                <div class="problems-row">
                    ${row.map(problem => `
                        <div class="problem-item">
                            ${problem}
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
        <style>
            .problems-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding: 5px;
            }
            
            .problems-row {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
            }
            
            .problem-item {
                font-size: 14px;
                line-height: 1.8;
                white-space: nowrap;
                padding: 0 5px;
            }
        </style>
    `;
    
    return html;
}