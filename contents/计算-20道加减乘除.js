// 生成随机数字
function randomNum(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成单个四则运算题
function generateProblem() {
    // 随机选择运算类型：加(0)、减(1)、乘(2)、除(3)
    const opType = Math.random();
    
    if (opType < 0.3) {  // 30% 概率加法
        const num1 = randomNum(1, 100);
        const num2 = randomNum(1, 100);
        return `${num1} + ${num2} = ______`;
    } 
    else if (opType < 0.6) {  // 30% 概率减法
        const larger = randomNum(50, 100);
        const smaller = randomNum(1, 49);
        return `${larger} - ${smaller} = ______`;
    }
    else if (opType < 0.8) {  // 20% 概率乘法
        const num1 = randomNum(2, 9);
        const num2 = randomNum(2, 9);
        return `${num1} × ${num2} = ______`;
    }
    else {  // 20% 概率除法
        const num2 = randomNum(2, 9);
        const result = randomNum(2, 9);
        const num1 = num2 * result;  // 确保能整除
        return `${num1} ÷ ${num2} = ______`;
    }
}

// 导出渲染函数
export default function render() {
    const problems = Array(20).fill(0).map(() => generateProblem());
    
    // 将题目分成 5 行 4 列
    let rows = [];
    for (let i = 0; i < 5; i++) {
        let row = problems.slice(i * 4, (i + 1) * 4);
        rows.push(row);
    }
    
    const html = `
        <div class="problems-container">
            ${rows.map(row => `
                <div class="problems-row">
                    ${row.map(problem => `
                        <div class="problem-item">
                            ${problem}
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
        <style>
            .problems-container {
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding: 5px;
            }
            
            .problems-row {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
            }
            
            .problem-item {
                font-size: 14px;
                line-height: 1.8;
                white-space: nowrap;
                padding: 0 5px;
            }
        </style>
    `;
    
    return html;
}
