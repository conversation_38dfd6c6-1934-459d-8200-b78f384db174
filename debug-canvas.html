<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas调试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        
        .test-page {
            width: 600px;
            height: 800px;
            background: white;
            border: 2px solid #333;
            margin: 20px;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .controls {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .log {
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h2>html2canvas调试工具</h2>
        <button onclick="testBasic()">基础测试</button>
        <button onclick="testWithOptions()">带选项测试</button>
        <button onclick="testA4Page()">A4页面测试</button>
        <button onclick="clearLog()">清空日志</button>
        <div id="log" class="log"></div>
    </div>

    <div class="test-page" id="simple-page">
        <h1 style="color: #333;">测试页面</h1>
        <p style="font-size: 16px; line-height: 1.5;">
            这是一个简单的测试页面，用来验证html2canvas是否能正常工作。
        </p>
        <div style="background: #e0e0e0; padding: 10px; margin: 10px 0;">
            <strong>测试内容：</strong>
            <ul>
                <li>文字渲染</li>
                <li>背景颜色</li>
                <li>边框样式</li>
                <li>布局结构</li>
            </ul>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 20px;">
            <div style="background: #ffebee; padding: 10px;">左侧内容</div>
            <div style="background: #e8f5e8; padding: 10px;">右侧内容</div>
        </div>
    </div>

    <div class="test-page" id="a4-page" style="width: 210mm; height: 297mm; display: none;">
        <div style="border-bottom: 2px solid #ccc; padding-bottom: 10px; margin-bottom: 20px;">
            <h2 style="margin: 0; color: #333;">数学练习题</h2>
            <div style="display: flex; justify-content: space-between; margin-top: 10px; font-size: 14px;">
                <span>日期：_______</span>
                <span>姓名：_______</span>
                <span>计时：_______</span>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>第一部分：加法练习</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div>1. 25 + 37 = ______</div>
                <div>2. 48 + 29 = ______</div>
                <div>3. 56 + 34 = ______</div>
                <div>4. 73 + 18 = ______</div>
            </div>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>第二部分：减法练习</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                <div>1. 85 - 37 = ______</div>
                <div>2. 74 - 28 = ______</div>
                <div>3. 92 - 45 = ______</div>
                <div>4. 67 - 29 = ______</div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>${time}: ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function downloadCanvas(canvas, filename) {
            const imageData = canvas.toDataURL('image/png', 1.0);
            const link = document.createElement('a');
            link.download = filename;
            link.href = imageData;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        async function testBasic() {
            log('开始基础测试...');
            
            if (!window.html2canvas) {
                log('❌ html2canvas未加载');
                return;
            }
            
            try {
                const element = document.getElementById('simple-page');
                const rect = element.getBoundingClientRect();
                log(`元素尺寸: ${rect.width}x${rect.height}`);
                
                const canvas = await html2canvas(element);
                log(`Canvas尺寸: ${canvas.width}x${canvas.height}`);
                
                if (canvas.width === 0 || canvas.height === 0) {
                    log('❌ Canvas尺寸为0');
                    return;
                }
                
                await downloadCanvas(canvas, 'basic-test.png');
                log('✅ 基础测试完成');
                
            } catch (error) {
                log('❌ 基础测试失败: ' + error.message);
                console.error(error);
            }
        }

        async function testWithOptions() {
            log('开始带选项测试...');
            
            try {
                const element = document.getElementById('simple-page');
                
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#FFFFFF',
                    logging: true
                });
                
                log(`Canvas尺寸: ${canvas.width}x${canvas.height}`);
                
                if (canvas.width === 0 || canvas.height === 0) {
                    log('❌ Canvas尺寸为0');
                    return;
                }
                
                await downloadCanvas(canvas, 'options-test.png');
                log('✅ 带选项测试完成');
                
            } catch (error) {
                log('❌ 带选项测试失败: ' + error.message);
                console.error(error);
            }
        }

        async function testA4Page() {
            log('开始A4页面测试...');
            
            try {
                const element = document.getElementById('a4-page');
                element.style.display = 'block';
                
                // 等待渲染
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const rect = element.getBoundingClientRect();
                log(`A4页面尺寸: ${rect.width}x${rect.height}`);
                
                const canvas = await html2canvas(element, {
                    scale: 1,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#FFFFFF',
                    logging: true
                });
                
                log(`Canvas尺寸: ${canvas.width}x${canvas.height}`);
                
                if (canvas.width === 0 || canvas.height === 0) {
                    log('❌ Canvas尺寸为0');
                    element.style.display = 'none';
                    return;
                }
                
                await downloadCanvas(canvas, 'a4-test.png');
                log('✅ A4页面测试完成');
                
                element.style.display = 'none';
                
            } catch (error) {
                log('❌ A4页面测试失败: ' + error.message);
                console.error(error);
            }
        }

        window.addEventListener('load', () => {
            log('页面加载完成');
            log('html2canvas版本: ' + (window.html2canvas ? '已加载' : '未加载'));
            
            // 显示页面信息
            const simplePage = document.getElementById('simple-page');
            const rect = simplePage.getBoundingClientRect();
            log(`简单页面尺寸: ${rect.width}x${rect.height}`);
        });
    </script>
</body>
</html>
