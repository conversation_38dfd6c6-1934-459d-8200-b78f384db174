<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多页面PDF调试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
</head>
<body>
    <h1>多页面PDF功能调试</h1>
    
    <div id="test-pages">
        <div class="test-page" style="width: 210mm; height: 297mm; background: white; border: 1px solid #ccc; margin: 10px; padding: 20mm; box-sizing: border-box;">
            <h2>第1页测试内容</h2>
            <p>这是第一页的测试内容。</p>
            <p>测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字</p>
        </div>
        
        <div class="test-page" style="width: 210mm; height: 297mm; background: white; border: 1px solid #ccc; margin: 10px; padding: 20mm; box-sizing: border-box;">
            <h2>第2页测试内容</h2>
            <p>这是第二页的测试内容。</p>
            <p>测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字</p>
        </div>
        
        <div class="test-page" style="width: 210mm; height: 297mm; background: white; border: 1px solid #ccc; margin: 10px; padding: 20mm; box-sizing: border-box;">
            <h2>第3页测试内容</h2>
            <p>这是第三页的测试内容。</p>
            <p>测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字测试文字</p>
        </div>
    </div>
    
    <button onclick="testPDF()" style="padding: 10px 20px; font-size: 16px; margin: 20px;">测试多页面PDF生成</button>
    
    <div id="status" style="margin: 20px; padding: 10px; background: #f0f0f0;"></div>
    
    <script>
        function log(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        async function testPDF() {
            log('开始测试PDF生成...');
            
            // 检查库是否加载
            if (!window.html2canvas) {
                log('❌ html2canvas未加载');
                return;
            }
            if (!window.jspdf) {
                log('❌ jsPDF未加载');
                return;
            }
            log('✅ 所有库已加载');
            
            const pages = document.querySelectorAll('.test-page');
            log(`找到 ${pages.length} 个测试页面`);
            
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });
                
                for (let i = 0; i < pages.length; i++) {
                    log(`正在处理第 ${i + 1} 页...`);
                    
                    if (i > 0) {
                        pdf.addPage();
                    }
                    
                    const canvas = await html2canvas(pages[i], {
                        scale: 2,
                        backgroundColor: '#FFFFFF',
                        width: 793,
                        height: 1122
                    });
                    
                    const imgData = canvas.toDataURL('image/jpeg', 1.0);
                    pdf.addImage(imgData, 'JPEG', 0, 0, 210, 297);
                    
                    log(`✅ 第 ${i + 1} 页已添加`);
                }
                
                log('生成PDF并打开...');
                const blob = pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url);
                
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                log('✅ PDF生成成功！');
                
            } catch (error) {
                log('❌ PDF生成失败: ' + error.message);
                console.error(error);
            }
        }
        
        // 页面加载完成后检查库
        window.addEventListener('load', () => {
            log('页面加载完成，检查库状态...');
            log('html2canvas: ' + (window.html2canvas ? '✅ 已加载' : '❌ 未加载'));
            log('jsPDF: ' + (window.jspdf ? '✅ 已加载' : '❌ 未加载'));
            log('html2pdf: ' + (window.html2pdf ? '✅ 已加载' : '❌ 未加载'));
        });
    </script>
</body>
</html>
