<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片导出测试</title>
    <link rel="stylesheet" href="styles/a4.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        body {
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-controls button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-controls button:hover {
            background: #45a049;
        }
        
        .status {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h2>图片导出测试</h2>
        <button onclick="testImageExport()">导出为图片</button>
        <button onclick="clearStatus()">清空日志</button>
        <div id="status" class="status"></div>
    </div>

    <div id="pages-container">
        <div class="print-page">
            <div class="page-header">
                <div class="header-top">
                    <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                    <div class="header-title">数学练习 - 第1页</div>
                </div>
                <div class="header-info">
                    <div class="info-cell">日期：2025-07-23</div>
                    <div class="info-cell">姓名：测试学生</div>
                    <div class="info-cell">计时：30分钟</div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="content-section">
                    <h3>第一部分：基础加法</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 16px;">
                        <div>1. 15 + 23 = ______</div>
                        <div>2. 34 + 17 = ______</div>
                        <div>3. 28 + 35 = ______</div>
                        <div>4. 46 + 29 = ______</div>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第二部分：基础减法</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 16px;">
                        <div>1. 67 - 23 = ______</div>
                        <div>2. 85 - 37 = ______</div>
                        <div>3. 74 - 28 = ______</div>
                        <div>4. 92 - 45 = ______</div>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第三部分：应用题</h3>
                    <div style="line-height: 1.8;">
                        <p><strong>1.</strong> 小明有25个苹果，小红有18个苹果，他们一共有多少个苹果？</p>
                        <p style="margin-left: 20px;">答：_________________</p>
                        <p><strong>2.</strong> 书架上有63本书，借走了27本，还剩多少本书？</p>
                        <p style="margin-left: 20px;">答：_________________</p>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第四部分：填空题</h3>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; font-size: 16px;">
                        <div>1. 15 + ( ) = 32</div>
                        <div>2. ( ) - 18 = 25</div>
                        <div>3. 24 + ( ) = 50</div>
                        <div>4. 67 - ( ) = 39</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="print-page" style="margin-top: 20px;">
            <div class="page-header">
                <div class="header-top">
                    <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                    <div class="header-title">数学练习 - 第2页</div>
                </div>
                <div class="header-info">
                    <div class="info-cell">日期：2025-07-23</div>
                    <div class="info-cell">姓名：测试学生</div>
                    <div class="info-cell">计时：30分钟</div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="content-section">
                    <h3>第一部分：进阶加法</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 16px;">
                        <div>1. 125 + 234 = ______</div>
                        <div>2. 367 + 189 = ______</div>
                        <div>3. 456 + 278 = ______</div>
                        <div>4. 589 + 345 = ______</div>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第二部分：进阶减法</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 16px;">
                        <div>1. 567 - 234 = ______</div>
                        <div>2. 785 - 367 = ______</div>
                        <div>3. 634 - 278 = ______</div>
                        <div>4. 892 - 445 = ______</div>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第三部分：乘除法</h3>
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; font-size: 16px;">
                        <div>1. 12 × 8 = ______</div>
                        <div>2. 144 ÷ 12 = ______</div>
                        <div>3. 15 × 6 = ______</div>
                        <div>4. 108 ÷ 9 = ______</div>
                    </div>
                </div>
                <div class="content-section">
                    <h3>第四部分：综合题</h3>
                    <div style="line-height: 1.8;">
                        <p><strong>1.</strong> 计算：25 + 18 - 12 = ______</p>
                        <p><strong>2.</strong> 计算：6 × 8 ÷ 4 = ______</p>
                        <p><strong>3.</strong> 计算：45 - 17 + 23 = ______</p>
                        <p><strong>4.</strong> 计算：72 ÷ 8 × 3 = ______</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const status = document.getElementById('status');
            const time = new Date().toLocaleTimeString();
            status.innerHTML += `<div>${time}: ${message}</div>`;
            status.scrollTop = status.scrollHeight;
            console.log(message);
        }

        function clearStatus() {
            document.getElementById('status').innerHTML = '';
        }

        async function testImageExport() {
            log('开始测试图片导出...');
            
            const printPages = document.querySelectorAll('.print-page');
            log(`找到 ${printPages.length} 个页面`);
            
            if (!window.html2canvas) {
                log('❌ html2canvas未加载');
                return;
            }
            
            try {
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                
                for (let i = 0; i < printPages.length; i++) {
                    log(`正在处理第 ${i + 1}/${printPages.length} 页...`);
                    
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    const pageRect = printPages[i].getBoundingClientRect();
                    log(`第 ${i + 1} 页尺寸: ${pageRect.width.toFixed(1)}x${pageRect.height.toFixed(1)}px`);
                    
                    const canvas = await html2canvas(printPages[i], {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        letterRendering: true,
                        backgroundColor: '#FFFFFF',
                        logging: false,
                        scrollX: 0,
                        scrollY: 0,
                        windowWidth: window.innerWidth,
                        windowHeight: window.innerHeight,
                        foreignObjectRendering: true,
                        removeContainer: true,
                        width: Math.max(pageRect.width, 793),
                        height: Math.max(pageRect.height, 1122)
                    });
                    
                    log(`第 ${i + 1} 页Canvas: ${canvas.width}x${canvas.height}`);
                    
                    const imageData = canvas.toDataURL('image/png', 1.0);
                    
                    const link = document.createElement('a');
                    link.download = `数学练习_第${i + 1}页_${timestamp}.png`;
                    link.href = imageData;
                    
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    log(`✅ 第 ${i + 1} 页图片已保存: ${link.download}`);
                    
                    if (i < printPages.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
                
                log(`🎉 所有 ${printPages.length} 页图片生成完成！`);
                alert(`成功生成 ${printPages.length} 张图片，已保存到下载文件夹`);
                
            } catch (error) {
                log('❌ 图片生成失败: ' + error.message);
                console.error(error);
            }
        }

        window.addEventListener('load', () => {
            log('页面加载完成');
            log('html2canvas: ' + (window.html2canvas ? '✅ 已加载' : '❌ 未加载'));
            
            const pages = document.querySelectorAll('.print-page');
            pages.forEach((page, index) => {
                const rect = page.getBoundingClientRect();
                log(`页面 ${index + 1} 尺寸: ${rect.width.toFixed(1)}x${rect.height.toFixed(1)}px`);
            });
        });
    </script>
</body>
</html>
