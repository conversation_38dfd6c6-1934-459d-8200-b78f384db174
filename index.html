<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>丹丹和蛋蛋的计算纸</title>
    <link rel="stylesheet" href="styles/a4.css">
    <link rel="stylesheet" href="styles/config.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 预览区 -->
        <div id="pages-container" class="pages-container">
            <!-- 页面将通过JavaScript动态生成 -->
            <div class="print-page">
                <!-- 页眉 -->
                <div class="page-header">
                    <div class="header-top">
                        <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                        <div class="header-title">老魏头的计算练习</div>
                    </div>
                    <div class="header-info">
                        <div class="info-cell">日期：</div>
                        <div class="info-cell">姓名：</div>
                        <div class="info-cell">计时：</div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div id="content-section-1" class="content-section"></div>
                    <div id="content-section-2" class="content-section"></div>
                    <div id="content-section-3" class="content-section"></div>
                    <div id="content-section-4" class="content-section"></div>
                </div>
            </div>
        </div>

        <!-- 配置区 -->
        <div class="config-area">
            <div class="config-section">
                <h3> 内容配置</h3>
                <div class="select-group">
                    <div class="select-item">
                        <label>第一部分</label>
                        <select id="section-1-selector" class="content-selector">
                            <option value="">-- 请选择练习内容 --</option>
                        </select>
                    </div>
                    <div class="select-item">
                        <label>第二部分</label>
                        <select id="section-2-selector" class="content-selector">
                            <option value="">-- 请选择练习内容 --</option>
                        </select>
                    </div>
                    <div class="select-item">
                        <label>第三部分</label>
                        <select id="section-3-selector" class="content-selector">
                            <option value="">-- 请选择练习内容 --</option>
                        </select>
                    </div>
                    <div class="select-item">
                        <label>第四部分</label>
                        <select id="section-4-selector" class="content-selector">
                            <option value="">-- 请选择练习内容 --</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3> 页面设置</h3>
                <div class="select-group">
                    <div class="select-item">
                        <label>生成页面数量</label>
                        <select id="page-count-selector" class="content-selector">
                            <option value="1">1页</option>
                            <option value="2">2页</option>
                            <option value="3">3页</option>
                            <option value="4">4页</option>
                            <option value="5">5页</option>
                            <option value="6">6页</option>
                            <option value="7">7页</option>
                            <option value="8">8页</option>
                            <option value="9">9页</option>
                            <option value="10">10页</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <button id="generate-button" onclick="generatePages()"> 生成练习题</button>
                <button id="print-button" onclick="exportToPDF()"> 保存为图片</button>
            </div>

            <div class="config-footer">
                Product by Duoduo & Dad
            </div>
        </div>
    </div>

    <script type="module" src="index.js"></script>
</body>
</html>