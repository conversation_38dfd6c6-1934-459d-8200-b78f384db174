import { contentList } from './contents/contentList.js';

// 动态加载下拉框选项
async function loadSelectOptions() {
    try {
        // 获取所有选择器
        const selectors = document.querySelectorAll('.content-selector');
        
        // 为每个选择器添加选项
        selectors.forEach(selector => {
            contentList.forEach(contentName => {
                const option = document.createElement('option');
                option.value = contentName;
                option.textContent = contentName;
                selector.appendChild(option);
            });
        });
    } catch (error) {
        console.error('加载选项失败:', error);
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 保存页面数量配置
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        config.pageCount = pageCountSelector.value;
    }

    localStorage.setItem('mathdrills-config', JSON.stringify(config));
}

// 从 localStorage 恢复配置
function loadConfig() {
    const savedConfig = localStorage.getItem('mathdrills-config');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        // 恢复内容选择器配置
        for (let i = 1; i <= 4; i++) {
            const selector = document.getElementById(`section-${i}-selector`);
            if (selector && config[`section-${i}`]) {
                selector.value = config[`section-${i}`];
            }
        }

        // 恢复页面数量配置
        const pageCountSelector = document.getElementById('page-count-selector');
        if (pageCountSelector && config.pageCount) {
            pageCountSelector.value = config.pageCount;
        }

        // 自动生成页面
        generatePages();
    }
}



// 生成多个页面
window.generatePages = async function() {
    const pageCountSelector = document.getElementById('page-count-selector');
    const pageCount = parseInt(pageCountSelector.value) || 1;

    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    // 清空现有页面
    pagesContainer.innerHTML = '';

    // 获取当前配置
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 生成指定数量的页面
    for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
        const pageElement = createPageElement(pageIndex + 1);
        pagesContainer.appendChild(pageElement);

        // 为每个页面加载内容
        for (let sectionId = 1; sectionId <= 4; sectionId++) {
            const contentName = config[`section-${sectionId}`];
            if (contentName) {
                await loadContentForPage(pageIndex, sectionId, contentName);
            }
        }
    }

    // 保存配置
    saveConfig();
};

// 为特定页面加载内容
async function loadContentForPage(pageIndex, sectionId, contentName) {
    if (!contentName) return;

    try {
        const module = await import(`./contents/${contentName}.js`);
        const containerId = `page-${pageIndex}-content-section-${sectionId}`;
        const sectionElement = document.getElementById(containerId);
        if (module.default && sectionElement) {
            sectionElement.innerHTML = module.default(containerId);
        } else {
            console.error('模块没有默认导出或区域元素不存在');
        }
    } catch (error) {
        console.error(`加载内容失败 (Page ${pageIndex}, Section ${sectionId}):`, error);
    }
}

// 创建单个页面元素
function createPageElement(pageNumber) {
    const pageDiv = document.createElement('div');
    pageDiv.className = 'print-page';
    pageDiv.innerHTML = `
        <!-- 页眉 -->
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">老魏头的计算练习 (第${pageNumber}页)</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：</div>
                <div class="info-cell">姓名：</div>
                <div class="info-cell">计时：</div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="content-area">
            <div id="page-${pageNumber - 1}-content-section-1" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-2" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-3" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-4" class="content-section"></div>
        </div>
    `;
    return pageDiv;
}

// PDF 导出配置
const pdfOptions = {
    margin: [10, 10, 10, 10],
    filename: '练习题.pdf',
    image: {
        type: 'jpeg',
        quality: 1.0
    },
    html2canvas: {
        scale: 2,
        useCORS: true,
        letterRendering: true,
        logging: true,
        scrollX: 0,
        scrollY: 0,
        backgroundColor: '#FFFFFF'
    },
    jsPDF: {
        unit: 'mm',
        format: 'a4',
        orientation: 'portrait',
        compress: true
    },
    pagebreak: { mode: ['avoid-all', 'css', 'legacy'] }
};

// 导出为 PDF 并打印
window.exportToPDF = async function() {
    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    const printPages = pagesContainer.querySelectorAll('.print-page');
    if (printPages.length === 0) {
        alert('请先生成练习题页面');
        return;
    }

    console.log(`开始生成 ${printPages.length} 页PDF...`);

    try {
        // 使用更简单的方法：为每个页面单独生成PDF，然后合并
        if (printPages.length === 1) {
            // 单页直接使用原有方法
            return await exportSinglePagePDF(printPages[0]);
        } else {
            // 多页使用新方法
            return await exportMultiPagePDF(printPages);
        }

    } catch (error) {
        console.error('PDF 生成失败:', error);
        alert('PDF生成失败，请检查浏览器控制台获取详细信息');
    }
};

// 单页PDF导出
async function exportSinglePagePDF(page) {
    console.log('使用单页PDF导出...');

    // 如果有jsPDF和html2canvas，使用改进的方法
    if (window.jspdf && window.html2canvas) {
        try {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4',
                compress: true
            });

            // 等待页面内容完全渲染
            await new Promise(resolve => setTimeout(resolve, 500));

            const canvas = await html2canvas(page, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                letterRendering: true,
                backgroundColor: '#FFFFFF',
                logging: false,
                scrollX: 0,
                scrollY: 0,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                foreignObjectRendering: true,
                removeContainer: true
            });

            const imgData = canvas.toDataURL('image/jpeg', 0.95);

            // 计算图片在PDF中的尺寸
            const pdfWidth = 210;
            const pdfHeight = 297;
            const imgAspectRatio = canvas.width / canvas.height;
            const pdfAspectRatio = pdfWidth / pdfHeight;

            let imgWidth, imgHeight;
            if (imgAspectRatio > pdfAspectRatio) {
                imgWidth = pdfWidth;
                imgHeight = pdfWidth / imgAspectRatio;
            } else {
                imgHeight = pdfHeight;
                imgWidth = pdfHeight * imgAspectRatio;
            }

            const x = (pdfWidth - imgWidth) / 2;
            const y = (pdfHeight - imgHeight) / 2;

            pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

            pdf.autoPrint();
            const blob = pdf.output('blob');
            const url = URL.createObjectURL(blob);
            window.open(url);
            setTimeout(() => URL.revokeObjectURL(url), 1000);

            console.log('单页PDF生成成功');
            return;

        } catch (error) {
            console.error('改进的单页PDF生成失败，回退到html2pdf:', error);
        }
    }

    // 回退到html2pdf方法
    try {
        const pdf = await html2pdf()
            .set(pdfOptions)
            .from(page)
            .toPdf()
            .get('pdf');

        pdf.autoPrint();
        const blob = await pdf.output('blob');
        const url = URL.createObjectURL(blob);
        window.open(url);
        setTimeout(() => URL.revokeObjectURL(url), 1000);

        console.log('html2pdf单页PDF生成成功');
    } catch (error) {
        console.error('单页PDF生成失败:', error);
    }
}

// 多页PDF导出
async function exportMultiPagePDF(printPages) {
    // 检查是否有jsPDF和html2canvas
    if (!window.jspdf || !window.html2canvas) {
        console.log('缺少必要库，使用html2pdf备用方法');
        return await exportToPDFLegacy(printPages);
    }

    const { jsPDF } = window.jspdf;
    const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
    });

    for (let i = 0; i < printPages.length; i++) {
        console.log(`处理第 ${i + 1}/${printPages.length} 页...`);

        if (i > 0) {
            pdf.addPage();
        }

        try {
            // 等待页面内容完全渲染
            await new Promise(resolve => setTimeout(resolve, 500));

            // 获取页面的实际尺寸
            const pageRect = printPages[i].getBoundingClientRect();
            console.log(`第 ${i + 1} 页尺寸:`, pageRect);

            // 使用更精确的html2canvas配置
            const canvas = await html2canvas(printPages[i], {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                letterRendering: true,
                backgroundColor: '#FFFFFF',
                logging: false,
                // 不设置固定的width/height，让html2canvas自动计算
                scrollX: 0,
                scrollY: 0,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                foreignObjectRendering: true,
                removeContainer: true
            });

            console.log(`第 ${i + 1} 页canvas尺寸: ${canvas.width}x${canvas.height}`);

            // 将canvas转换为图片并添加到PDF
            const imgData = canvas.toDataURL('image/jpeg', 0.95);

            // 计算图片在PDF中的尺寸，保持宽高比
            const pdfWidth = 210; // A4宽度(mm)
            const pdfHeight = 297; // A4高度(mm)
            const imgAspectRatio = canvas.width / canvas.height;
            const pdfAspectRatio = pdfWidth / pdfHeight;

            let imgWidth, imgHeight;
            if (imgAspectRatio > pdfAspectRatio) {
                // 图片更宽，以宽度为准
                imgWidth = pdfWidth;
                imgHeight = pdfWidth / imgAspectRatio;
            } else {
                // 图片更高，以高度为准
                imgHeight = pdfHeight;
                imgWidth = pdfHeight * imgAspectRatio;
            }

            // 居中放置
            const x = (pdfWidth - imgWidth) / 2;
            const y = (pdfHeight - imgHeight) / 2;

            pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

            console.log(`✅ 第 ${i + 1} 页已添加 (${imgWidth.toFixed(1)}x${imgHeight.toFixed(1)}mm)`);

        } catch (error) {
            console.error(`第 ${i + 1} 页处理失败:`, error);
            // 继续处理下一页
        }
    }

    // 生成并打开PDF
    pdf.autoPrint();
    const blob = pdf.output('blob');
    const url = URL.createObjectURL(blob);
    window.open(url);
    setTimeout(() => URL.revokeObjectURL(url), 1000);

    console.log(`✅ 多页PDF生成成功，共 ${printPages.length} 页`);
}



// 备用PDF导出方法（使用html2pdf）
async function exportToPDFLegacy(printPages) {
    console.log('使用html2pdf备用方法生成PDF...');

    try {
        // 创建一个包含完整样式的容器
        const container = document.createElement('div');
        container.style.visibility = 'hidden';
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '0';
        document.body.appendChild(container);

        // 复制所有相关的样式表
        const styles = document.querySelectorAll('style, link[rel="stylesheet"]');
        styles.forEach(style => {
            container.appendChild(style.cloneNode(true));
        });

        // 创建多页面容器，使用特殊的分页样式
        const pagesDiv = document.createElement('div');
        pagesDiv.style.backgroundColor = '#FFFFFF';

        // 复制所有页面，使用CSS分页
        printPages.forEach((page, index) => {
            const pageDiv = document.createElement('div');
            pageDiv.className = 'print-page';
            pageDiv.style.width = '210mm';
            pageDiv.style.height = '297mm';
            pageDiv.style.padding = '10mm';
            pageDiv.style.margin = '0';
            pageDiv.style.backgroundColor = '#FFFFFF';
            pageDiv.style.boxSizing = 'border-box';
            pageDiv.style.overflow = 'hidden';
            pageDiv.style.breakAfter = 'page';

            // 最后一页不需要分页符
            if (index === printPages.length - 1) {
                pageDiv.style.breakAfter = 'auto';
            }

            pageDiv.innerHTML = page.innerHTML;
            pagesDiv.appendChild(pageDiv);
        });

        container.appendChild(pagesDiv);

        // 等待样式加载
        await new Promise(resolve => setTimeout(resolve, 100));

        // 等待图片加载
        const images = container.getElementsByTagName('img');
        await Promise.all(Array.from(images).map(img => {
            if (img.complete) return Promise.resolve();
            return new Promise(resolve => {
                img.onload = resolve;
                img.onerror = resolve;
            });
        }));

        // 确保内容完全渲染
        await new Promise(resolve => setTimeout(resolve, 500));

        try {
            // 生成 PDF
            const pdf = await html2pdf()
                .set(pdfOptions)
                .from(pagesDiv)
                .toPdf()
                .get('pdf');

            // 打开预览并打印
            pdf.autoPrint();
            const blob = await pdf.output('blob');
            const url = URL.createObjectURL(blob);
            window.open(url);

            // 清理 URL
            setTimeout(() => URL.revokeObjectURL(url), 1000);

        } finally {
            // 清理临时容器
            document.body.removeChild(container);
        }

        console.log('备用PDF生成成功');
    } catch (error) {
        console.error('备用PDF生成也失败:', error);
        alert('PDF生成失败，请检查浏览器控制台获取详细信息');
    }
}

// 初始化页面
function init() {
    // 首先加载选项
    loadSelectOptions();

    // 为每个选择器添加事件监听
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            selector.addEventListener('change', () => {
                saveConfig();  // 保存配置
                // 不自动重新生成，让用户手动点击生成按钮
            });
        }
    }

    // 为页面数量选择器添加事件监听
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        pageCountSelector.addEventListener('change', () => {
            saveConfig();  // 保存配置
        });
    }

    // 加载保存的配置
    loadConfig();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);