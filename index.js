import { contentList } from './contents/contentList.js';

// 动态加载下拉框选项
async function loadSelectOptions() {
    try {
        // 获取所有选择器
        const selectors = document.querySelectorAll('.content-selector');
        
        // 为每个选择器添加选项
        selectors.forEach(selector => {
            contentList.forEach(contentName => {
                const option = document.createElement('option');
                option.value = contentName;
                option.textContent = contentName;
                selector.appendChild(option);
            });
        });
    } catch (error) {
        console.error('加载选项失败:', error);
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 保存页面数量配置
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        config.pageCount = pageCountSelector.value;
    }

    localStorage.setItem('mathdrills-config', JSON.stringify(config));
}

// 从 localStorage 恢复配置
function loadConfig() {
    const savedConfig = localStorage.getItem('mathdrills-config');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        // 恢复内容选择器配置
        for (let i = 1; i <= 4; i++) {
            const selector = document.getElementById(`section-${i}-selector`);
            if (selector && config[`section-${i}`]) {
                selector.value = config[`section-${i}`];
            }
        }

        // 恢复页面数量配置
        const pageCountSelector = document.getElementById('page-count-selector');
        if (pageCountSelector && config.pageCount) {
            pageCountSelector.value = config.pageCount;
        }

        // 自动生成页面
        generatePages();
    }
}



// 生成多个页面
window.generatePages = async function() {
    const pageCountSelector = document.getElementById('page-count-selector');
    const pageCount = parseInt(pageCountSelector.value) || 1;

    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    // 清空现有页面
    pagesContainer.innerHTML = '';

    // 获取当前配置
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 生成指定数量的页面
    for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
        const pageElement = createPageElement(pageIndex + 1);
        pagesContainer.appendChild(pageElement);

        // 为每个页面加载内容
        for (let sectionId = 1; sectionId <= 4; sectionId++) {
            const contentName = config[`section-${sectionId}`];
            if (contentName) {
                await loadContentForPage(pageIndex, sectionId, contentName);
            }
        }
    }

    // 保存配置
    saveConfig();
};

// 为特定页面加载内容
async function loadContentForPage(pageIndex, sectionId, contentName) {
    if (!contentName) return;

    try {
        const module = await import(`./contents/${contentName}.js`);
        const containerId = `page-${pageIndex}-content-section-${sectionId}`;
        const sectionElement = document.getElementById(containerId);
        if (module.default && sectionElement) {
            sectionElement.innerHTML = module.default(containerId);
        } else {
            console.error('模块没有默认导出或区域元素不存在');
        }
    } catch (error) {
        console.error(`加载内容失败 (Page ${pageIndex}, Section ${sectionId}):`, error);
    }
}

// 创建单个页面元素
function createPageElement(pageNumber) {
    const pageDiv = document.createElement('div');
    pageDiv.className = 'print-page';
    pageDiv.innerHTML = `
        <!-- 页眉 -->
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">老魏头的计算练习 (第${pageNumber}页)</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：</div>
                <div class="info-cell">姓名：</div>
                <div class="info-cell">计时：</div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="content-area">
            <div id="page-${pageNumber - 1}-content-section-1" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-2" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-3" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-4" class="content-section"></div>
        </div>
    `;
    return pageDiv;
}



// 导出为图片并下载
window.exportToPDF = async function() {
    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    const printPages = pagesContainer.querySelectorAll('.print-page');
    if (printPages.length === 0) {
        alert('请先生成练习题页面');
        return;
    }

    console.log(`开始生成 ${printPages.length} 页图片...`);

    try {
        await exportPagesToImages(printPages);

    } catch (error) {
        console.error('图片生成失败:', error);
        alert('图片生成失败，请检查浏览器控制台获取详细信息');
    }
};

// 将页面导出为图片并下载
async function exportPagesToImages(printPages) {
    console.log(`开始将 ${printPages.length} 页转换为图片...`);

    if (!window.html2canvas) {
        alert('html2canvas库未加载，无法生成图片');
        return;
    }

    try {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');

        for (let i = 0; i < printPages.length; i++) {
            console.log(`正在处理第 ${i + 1}/${printPages.length} 页...`);

            // 等待页面渲染
            await new Promise(resolve => setTimeout(resolve, 500));

            // 获取页面尺寸
            const pageRect = printPages[i].getBoundingClientRect();
            console.log(`第 ${i + 1} 页尺寸: ${pageRect.width.toFixed(1)}x${pageRect.height.toFixed(1)}px`);

            // 使用html2canvas转换页面为图片
            const canvas = await html2canvas(printPages[i], {
                scale: 2, // 高分辨率
                useCORS: true,
                allowTaint: true,
                letterRendering: true,
                backgroundColor: '#FFFFFF',
                logging: false,
                scrollX: 0,
                scrollY: 0,
                windowWidth: window.innerWidth,
                windowHeight: window.innerHeight,
                foreignObjectRendering: true,
                removeContainer: true,
                // 设置固定尺寸确保A4比例
                width: Math.max(pageRect.width, 793), // A4宽度对应的像素
                height: Math.max(pageRect.height, 1122) // A4高度对应的像素
            });

            console.log(`第 ${i + 1} 页Canvas尺寸: ${canvas.width}x${canvas.height}`);

            // 将canvas转换为图片数据
            const imageData = canvas.toDataURL('image/png', 1.0);

            // 创建下载链接
            const link = document.createElement('a');
            link.download = `练习题_第${i + 1}页_${timestamp}.png`;
            link.href = imageData;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log(`✅ 第 ${i + 1} 页图片已保存: ${link.download}`);

            // 短暂延迟避免浏览器阻止多个下载
            if (i < printPages.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log(`🎉 所有 ${printPages.length} 页图片生成完成！`);
        alert(`成功生成 ${printPages.length} 张图片，已保存到下载文件夹`);

    } catch (error) {
        console.error('图片生成失败:', error);
        throw error;
    }
}







// 初始化页面
function init() {
    // 首先加载选项
    loadSelectOptions();

    // 为每个选择器添加事件监听
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            selector.addEventListener('change', () => {
                saveConfig();  // 保存配置
                // 不自动重新生成，让用户手动点击生成按钮
            });
        }
    }

    // 为页面数量选择器添加事件监听
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        pageCountSelector.addEventListener('change', () => {
            saveConfig();  // 保存配置
        });
    }

    // 加载保存的配置
    loadConfig();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);