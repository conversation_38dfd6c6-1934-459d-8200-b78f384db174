import { contentList } from './contents/contentList.js';

// 动态加载下拉框选项
async function loadSelectOptions() {
    try {
        // 获取所有选择器
        const selectors = document.querySelectorAll('.content-selector');
        
        // 为每个选择器添加选项
        selectors.forEach(selector => {
            contentList.forEach(contentName => {
                const option = document.createElement('option');
                option.value = contentName;
                option.textContent = contentName;
                selector.appendChild(option);
            });
        });
    } catch (error) {
        console.error('加载选项失败:', error);
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 保存页面数量配置
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        config.pageCount = pageCountSelector.value;
    }

    localStorage.setItem('mathdrills-config', JSON.stringify(config));
}

// 从 localStorage 恢复配置
function loadConfig() {
    const savedConfig = localStorage.getItem('mathdrills-config');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        // 恢复内容选择器配置
        for (let i = 1; i <= 4; i++) {
            const selector = document.getElementById(`section-${i}-selector`);
            if (selector && config[`section-${i}`]) {
                selector.value = config[`section-${i}`];
            }
        }

        // 恢复页面数量配置
        const pageCountSelector = document.getElementById('page-count-selector');
        if (pageCountSelector && config.pageCount) {
            pageCountSelector.value = config.pageCount;
        }

        // 自动生成页面
        generatePages();
    }
}



// 生成多个页面
window.generatePages = async function() {
    const pageCountSelector = document.getElementById('page-count-selector');
    const pageCount = parseInt(pageCountSelector.value) || 1;

    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    // 清空现有页面
    pagesContainer.innerHTML = '';

    // 获取当前配置
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 生成指定数量的页面
    for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
        const pageElement = createPageElement(pageIndex + 1);
        pagesContainer.appendChild(pageElement);

        // 为每个页面加载内容
        for (let sectionId = 1; sectionId <= 4; sectionId++) {
            const contentName = config[`section-${sectionId}`];
            if (contentName) {
                await loadContentForPage(pageIndex, sectionId, contentName);
            }
        }
    }

    // 保存配置
    saveConfig();
};

// 为特定页面加载内容
async function loadContentForPage(pageIndex, sectionId, contentName) {
    if (!contentName) return;

    try {
        const module = await import(`./contents/${contentName}.js`);
        const containerId = `page-${pageIndex}-content-section-${sectionId}`;
        const sectionElement = document.getElementById(containerId);
        if (module.default && sectionElement) {
            sectionElement.innerHTML = module.default(containerId);
        } else {
            console.error('模块没有默认导出或区域元素不存在');
        }
    } catch (error) {
        console.error(`加载内容失败 (Page ${pageIndex}, Section ${sectionId}):`, error);
    }
}

// 创建单个页面元素
function createPageElement(pageNumber) {
    const pageDiv = document.createElement('div');
    pageDiv.className = 'print-page';
    pageDiv.innerHTML = `
        <!-- 页眉 -->
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">老魏头的计算练习 (第${pageNumber}页)</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：</div>
                <div class="info-cell">姓名：</div>
                <div class="info-cell">计时：</div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="content-area">
            <div id="page-${pageNumber - 1}-content-section-1" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-2" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-3" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-4" class="content-section"></div>
        </div>
    `;
    return pageDiv;
}



// 使用Puppeteer后端生成PDF
window.exportToPDF = async function() {
    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    const printPages = pagesContainer.querySelectorAll('.print-page');
    if (printPages.length === 0) {
        alert('请先生成练习题页面');
        return;
    }

    console.log(`开始生成 ${printPages.length} 页PDF...`);

    try {
        await generatePDFWithPuppeteer(printPages);

    } catch (error) {
        console.error('PDF生成失败:', error);
        alert('PDF生成失败，请检查浏览器控制台获取详细信息');
    }
};

// 使用Puppeteer后端生成PDF
async function generatePDFWithPuppeteer(printPages) {
    console.log(`开始使用Puppeteer生成 ${printPages.length} 页PDF...`);

    try {
        // 显示加载状态
        const loadingDiv = showLoading('正在生成PDF，请稍候...');

        // 收集页面数据
        const pagesData = [];
        for (let i = 0; i < printPages.length; i++) {
            pagesData.push({
                html: printPages[i].innerHTML,
                index: i + 1
            });
        }

        // 调用后端API
        const response = await fetch('http://localhost:3000/api/generate-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pages: pagesData,
                filename: `数学练习题_${pagesData.length}页_${new Date().toISOString().slice(0, 10)}.pdf`
            })
        });

        hideLoading(loadingDiv);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'PDF生成失败');
        }

        // 下载PDF
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `数学练习题_${pagesData.length}页.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        console.log(`✅ PDF生成成功，共 ${pagesData.length} 页`);
        alert(`PDF生成成功！共 ${pagesData.length} 页，已开始下载。`);

    } catch (error) {
        console.error('PDF生成失败:', error);

        if (error.message.includes('fetch')) {
            alert('无法连接到PDF生成服务，请确保后端服务器正在运行。\n\n启动命令：npm run server');
        } else {
            alert(`PDF生成失败：${error.message}`);
        }

        throw error;
    }
}

// 显示加载状态
function showLoading(message) {
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-overlay';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        color: white;
        font-size: 18px;
        font-family: Arial, sans-serif;
    `;
    loadingDiv.innerHTML = `
        <div style="text-align: center;">
            <div style="margin-bottom: 20px;">⏳</div>
            <div>${message}</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
    return loadingDiv;
}

// 隐藏加载状态
function hideLoading(loadingDiv) {
    if (loadingDiv && loadingDiv.parentNode) {
        loadingDiv.parentNode.removeChild(loadingDiv);
    }
}









// 初始化页面
function init() {
    // 首先加载选项
    loadSelectOptions();

    // 为每个选择器添加事件监听
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            selector.addEventListener('change', () => {
                saveConfig();  // 保存配置
                // 不自动重新生成，让用户手动点击生成按钮
            });
        }
    }

    // 为页面数量选择器添加事件监听
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        pageCountSelector.addEventListener('change', () => {
            saveConfig();  // 保存配置
        });
    }

    // 加载保存的配置
    loadConfig();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);