import { contentList } from './contents/contentList.js';

// 动态加载下拉框选项
async function loadSelectOptions() {
    try {
        // 获取所有选择器
        const selectors = document.querySelectorAll('.content-selector');
        
        // 为每个选择器添加选项
        selectors.forEach(selector => {
            contentList.forEach(contentName => {
                const option = document.createElement('option');
                option.value = contentName;
                option.textContent = contentName;
                selector.appendChild(option);
            });
        });
    } catch (error) {
        console.error('加载选项失败:', error);
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 保存页面数量配置
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        config.pageCount = pageCountSelector.value;
    }

    localStorage.setItem('mathdrills-config', JSON.stringify(config));
}

// 从 localStorage 恢复配置
function loadConfig() {
    const savedConfig = localStorage.getItem('mathdrills-config');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        // 恢复内容选择器配置
        for (let i = 1; i <= 4; i++) {
            const selector = document.getElementById(`section-${i}-selector`);
            if (selector && config[`section-${i}`]) {
                selector.value = config[`section-${i}`];
            }
        }

        // 恢复页面数量配置
        const pageCountSelector = document.getElementById('page-count-selector');
        if (pageCountSelector && config.pageCount) {
            pageCountSelector.value = config.pageCount;
        }

        // 自动生成页面
        generatePages();
    }
}



// 生成多个页面
window.generatePages = async function() {
    const pageCountSelector = document.getElementById('page-count-selector');
    const pageCount = parseInt(pageCountSelector.value) || 1;

    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    // 清空现有页面
    pagesContainer.innerHTML = '';

    // 获取当前配置
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 生成指定数量的页面
    for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
        const pageElement = createPageElement(pageIndex + 1);
        pagesContainer.appendChild(pageElement);

        // 为每个页面加载内容
        for (let sectionId = 1; sectionId <= 4; sectionId++) {
            const contentName = config[`section-${sectionId}`];
            if (contentName) {
                await loadContentForPage(pageIndex, sectionId, contentName);
            }
        }
    }

    // 保存配置
    saveConfig();
};

// 为特定页面加载内容
async function loadContentForPage(pageIndex, sectionId, contentName) {
    if (!contentName) return;

    try {
        const module = await import(`./contents/${contentName}.js`);
        const containerId = `page-${pageIndex}-content-section-${sectionId}`;
        const sectionElement = document.getElementById(containerId);
        if (module.default && sectionElement) {
            sectionElement.innerHTML = module.default(containerId);
        } else {
            console.error('模块没有默认导出或区域元素不存在');
        }
    } catch (error) {
        console.error(`加载内容失败 (Page ${pageIndex}, Section ${sectionId}):`, error);
    }
}

// 创建单个页面元素
function createPageElement(pageNumber) {
    const pageDiv = document.createElement('div');
    pageDiv.className = 'print-page';
    pageDiv.innerHTML = `
        <!-- 页眉 -->
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">老魏头的计算练习 (第${pageNumber}页)</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：</div>
                <div class="info-cell">姓名：</div>
                <div class="info-cell">计时：</div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="content-area">
            <div id="page-${pageNumber - 1}-content-section-1" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-2" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-3" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-4" class="content-section"></div>
        </div>
    `;
    return pageDiv;
}



// 导出为 PDF 并打印
window.exportToPDF = async function() {
    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    const printPages = pagesContainer.querySelectorAll('.print-page');
    if (printPages.length === 0) {
        alert('请先生成练习题页面');
        return;
    }

    console.log(`开始生成 ${printPages.length} 页PDF...`);

    try {
        // 统一使用html2pdf方法，这是最可靠的
        await exportWithHtml2PDF(printPages);

    } catch (error) {
        console.error('PDF 生成失败:', error);
        alert('PDF生成失败，请检查浏览器控制台获取详细信息');
    }
};

// 使用html2pdf导出多页面PDF
async function exportWithHtml2PDF(printPages) {
    console.log(`使用html2pdf生成 ${printPages.length} 页PDF...`);

    try {
        // 创建一个临时容器来包含所有页面
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '0';
        tempContainer.style.visibility = 'hidden';
        document.body.appendChild(tempContainer);

        // 复制所有样式表到临时容器
        const styles = document.querySelectorAll('style, link[rel="stylesheet"]');
        styles.forEach(style => {
            const clonedStyle = style.cloneNode(true);
            tempContainer.appendChild(clonedStyle);
        });

        // 创建包含所有页面的容器
        const pagesWrapper = document.createElement('div');
        pagesWrapper.style.backgroundColor = '#FFFFFF';

        // 复制每个页面
        printPages.forEach((page, index) => {
            const pageClone = page.cloneNode(true);

            // 确保页面样式正确
            pageClone.style.width = '210mm';
            pageClone.style.height = '297mm';
            pageClone.style.margin = '0';
            pageClone.style.padding = '10mm';
            pageClone.style.backgroundColor = '#FFFFFF';
            pageClone.style.boxSizing = 'border-box';
            pageClone.style.overflow = 'hidden';
            pageClone.style.display = 'block';
            pageClone.style.position = 'relative';

            // 添加分页符（除了最后一页）
            if (index < printPages.length - 1) {
                pageClone.style.pageBreakAfter = 'always';
                pageClone.style.breakAfter = 'page';
            }

            pagesWrapper.appendChild(pageClone);
        });

        tempContainer.appendChild(pagesWrapper);

        // 等待内容渲染
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 等待图片加载
        const images = tempContainer.getElementsByTagName('img');
        await Promise.all(Array.from(images).map(img => {
            if (img.complete) return Promise.resolve();
            return new Promise(resolve => {
                img.onload = resolve;
                img.onerror = resolve;
                // 设置超时避免无限等待
                setTimeout(resolve, 3000);
            });
        }));

        console.log('开始生成PDF...');

        // 使用html2pdf生成PDF
        const pdf = await html2pdf()
            .set({
                margin: [10, 10, 10, 10],
                filename: `练习题_${printPages.length}页.pdf`,
                image: {
                    type: 'jpeg',
                    quality: 0.98
                },
                html2canvas: {
                    scale: 2,
                    useCORS: true,
                    letterRendering: true,
                    logging: false,
                    backgroundColor: '#FFFFFF',
                    allowTaint: true,
                    foreignObjectRendering: true
                },
                jsPDF: {
                    unit: 'mm',
                    format: 'a4',
                    orientation: 'portrait',
                    compress: true
                },
                pagebreak: {
                    mode: ['avoid-all', 'css', 'legacy'],
                    before: '.print-page',
                    after: '.print-page'
                }
            })
            .from(pagesWrapper)
            .toPdf()
            .get('pdf');

        // 清理临时容器
        document.body.removeChild(tempContainer);

        // 打开PDF
        pdf.autoPrint();
        const blob = await pdf.output('blob');
        const url = URL.createObjectURL(blob);
        window.open(url);

        // 清理URL
        setTimeout(() => URL.revokeObjectURL(url), 1000);

        console.log(`✅ PDF生成成功，共 ${printPages.length} 页`);

    } catch (error) {
        console.error('PDF生成失败:', error);
        throw error;
    }
}







// 初始化页面
function init() {
    // 首先加载选项
    loadSelectOptions();

    // 为每个选择器添加事件监听
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            selector.addEventListener('change', () => {
                saveConfig();  // 保存配置
                // 不自动重新生成，让用户手动点击生成按钮
            });
        }
    }

    // 为页面数量选择器添加事件监听
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        pageCountSelector.addEventListener('change', () => {
            saveConfig();  // 保存配置
        });
    }

    // 加载保存的配置
    loadConfig();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);