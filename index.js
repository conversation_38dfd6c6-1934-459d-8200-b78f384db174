import { contentList } from './contents/contentList.js';

// 动态加载下拉框选项
async function loadSelectOptions() {
    try {
        // 获取所有选择器
        const selectors = document.querySelectorAll('.content-selector');
        
        // 为每个选择器添加选项
        selectors.forEach(selector => {
            contentList.forEach(contentName => {
                const option = document.createElement('option');
                option.value = contentName;
                option.textContent = contentName;
                selector.appendChild(option);
            });
        });
    } catch (error) {
        console.error('加载选项失败:', error);
    }
}

// 保存配置到 localStorage
function saveConfig() {
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 保存页面数量配置
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        config.pageCount = pageCountSelector.value;
    }

    localStorage.setItem('mathdrills-config', JSON.stringify(config));
}

// 从 localStorage 恢复配置
function loadConfig() {
    const savedConfig = localStorage.getItem('mathdrills-config');
    if (savedConfig) {
        const config = JSON.parse(savedConfig);

        // 恢复内容选择器配置
        for (let i = 1; i <= 4; i++) {
            const selector = document.getElementById(`section-${i}-selector`);
            if (selector && config[`section-${i}`]) {
                selector.value = config[`section-${i}`];
            }
        }

        // 恢复页面数量配置
        const pageCountSelector = document.getElementById('page-count-selector');
        if (pageCountSelector && config.pageCount) {
            pageCountSelector.value = config.pageCount;
        }

        // 自动生成页面
        generatePages();
    }
}



// 生成多个页面
window.generatePages = async function() {
    const pageCountSelector = document.getElementById('page-count-selector');
    const pageCount = parseInt(pageCountSelector.value) || 1;

    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    // 清空现有页面
    pagesContainer.innerHTML = '';

    // 获取当前配置
    const config = {};
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            config[`section-${i}`] = selector.value;
        }
    }

    // 生成指定数量的页面
    for (let pageIndex = 0; pageIndex < pageCount; pageIndex++) {
        const pageElement = createPageElement(pageIndex + 1);
        pagesContainer.appendChild(pageElement);

        // 为每个页面加载内容
        for (let sectionId = 1; sectionId <= 4; sectionId++) {
            const contentName = config[`section-${sectionId}`];
            if (contentName) {
                await loadContentForPage(pageIndex, sectionId, contentName);
            }
        }
    }

    // 保存配置
    saveConfig();
};

// 为特定页面加载内容
async function loadContentForPage(pageIndex, sectionId, contentName) {
    if (!contentName) return;

    try {
        const module = await import(`./contents/${contentName}.js`);
        const containerId = `page-${pageIndex}-content-section-${sectionId}`;
        const sectionElement = document.getElementById(containerId);
        if (module.default && sectionElement) {
            sectionElement.innerHTML = module.default(containerId);
        } else {
            console.error('模块没有默认导出或区域元素不存在');
        }
    } catch (error) {
        console.error(`加载内容失败 (Page ${pageIndex}, Section ${sectionId}):`, error);
    }
}

// 创建单个页面元素
function createPageElement(pageNumber) {
    const pageDiv = document.createElement('div');
    pageDiv.className = 'print-page';
    pageDiv.innerHTML = `
        <!-- 页眉 -->
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">老魏头的计算练习 (第${pageNumber}页)</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：</div>
                <div class="info-cell">姓名：</div>
                <div class="info-cell">计时：</div>
            </div>
        </div>

        <!-- 内容区 -->
        <div class="content-area">
            <div id="page-${pageNumber - 1}-content-section-1" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-2" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-3" class="content-section"></div>
            <div id="page-${pageNumber - 1}-content-section-4" class="content-section"></div>
        </div>
    `;
    return pageDiv;
}



// 导出为图片并下载
window.exportToPDF = async function() {
    const pagesContainer = document.getElementById('pages-container');
    if (!pagesContainer) return;

    const printPages = pagesContainer.querySelectorAll('.print-page');
    if (printPages.length === 0) {
        alert('请先生成练习题页面');
        return;
    }

    console.log(`开始生成 ${printPages.length} 页图片...`);

    try {
        await exportPagesToImages(printPages);

    } catch (error) {
        console.error('图片生成失败:', error);
        alert('图片生成失败，请检查浏览器控制台获取详细信息');
    }
};

// 将页面导出为图片并下载
async function exportPagesToImages(printPages) {
    console.log(`开始将 ${printPages.length} 页转换为图片...`);

    if (!window.html2canvas) {
        alert('html2canvas库未加载，无法生成图片');
        return;
    }

    try {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');

        for (let i = 0; i < printPages.length; i++) {
            console.log(`正在处理第 ${i + 1}/${printPages.length} 页...`);

            // 创建页面的临时副本，使用像素单位
            const tempPage = await createTempPageForCanvas(printPages[i]);

            try {
                // 等待渲染
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 获取页面尺寸
                const pageRect = tempPage.getBoundingClientRect();
                console.log(`第 ${i + 1} 页尺寸: ${pageRect.width.toFixed(1)}x${pageRect.height.toFixed(1)}px`);

                if (pageRect.width === 0 || pageRect.height === 0) {
                    console.error(`第 ${i + 1} 页尺寸为0，跳过`);
                    continue;
                }

                // 使用html2canvas转换页面为图片
                const canvas = await html2canvas(tempPage, {
                    scale: 2, // 高分辨率
                    useCORS: true,
                    allowTaint: true,
                    letterRendering: true,
                    backgroundColor: '#FFFFFF',
                    logging: true, // 开启日志调试
                    scrollX: 0,
                    scrollY: 0
                });

                console.log(`第 ${i + 1} 页Canvas尺寸: ${canvas.width}x${canvas.height}`);

                if (canvas.width === 0 || canvas.height === 0) {
                    console.error(`第 ${i + 1} 页Canvas尺寸为0，跳过`);
                    continue;
                }

                // 将canvas转换为图片数据
                const imageData = canvas.toDataURL('image/png', 1.0);

                // 创建下载链接
                const link = document.createElement('a');
                link.download = `练习题_第${i + 1}页_${timestamp}.png`;
                link.href = imageData;

                // 触发下载
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log(`✅ 第 ${i + 1} 页图片已保存: ${link.download}`);

            } finally {
                // 清理临时页面
                document.body.removeChild(tempPage);
            }

            // 短暂延迟避免浏览器阻止多个下载
            if (i < printPages.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        console.log(`🎉 所有 ${printPages.length} 页图片生成完成！`);
        alert(`成功生成 ${printPages.length} 张图片，已保存到下载文件夹`);

    } catch (error) {
        console.error('图片生成失败:', error);
        throw error;
    }
}

// 创建用于Canvas的临时页面副本（使用像素单位）
async function createTempPageForCanvas(originalPage) {
    // 创建临时容器
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '0';
    tempContainer.style.visibility = 'hidden';
    tempContainer.style.zIndex = '-1000';
    document.body.appendChild(tempContainer);

    // 复制样式表
    const styles = document.querySelectorAll('style, link[rel="stylesheet"]');
    styles.forEach(style => {
        const clonedStyle = style.cloneNode(true);
        tempContainer.appendChild(clonedStyle);
    });

    // 创建页面副本
    const pageClone = originalPage.cloneNode(true);

    // 将mm单位转换为像素单位 (1mm ≈ 3.78px at 96dpi)
    pageClone.style.width = '793px';  // 210mm
    pageClone.style.height = '1122px'; // 297mm
    pageClone.style.padding = '38px';  // 10mm
    pageClone.style.margin = '0';
    pageClone.style.backgroundColor = '#FFFFFF';
    pageClone.style.boxSizing = 'border-box';
    pageClone.style.overflow = 'hidden';
    pageClone.style.display = 'block';
    pageClone.style.position = 'relative';
    pageClone.style.fontFamily = 'Arial, sans-serif';

    // 修复页眉样式
    const pageHeader = pageClone.querySelector('.page-header');
    if (pageHeader) {
        pageHeader.style.height = '95px'; // 25mm
        pageHeader.style.position = 'absolute';
        pageHeader.style.top = '38px';    // 10mm
        pageHeader.style.left = '38px';   // 10mm
        pageHeader.style.right = '38px';  // 10mm
    }

    // 修复内容区域样式
    const contentArea = pageClone.querySelector('.content-area');
    if (contentArea) {
        contentArea.style.position = 'absolute';
        contentArea.style.top = '133px';  // 38px + 95px (margin + header)
        contentArea.style.left = '38px';  // 10mm
        contentArea.style.right = '38px'; // 10mm
        contentArea.style.height = '951px'; // 剩余高度
        contentArea.style.display = 'flex';
        contentArea.style.flexDirection = 'column';
    }

    // 修复内容区块样式
    const contentSections = pageClone.querySelectorAll('.content-section');
    contentSections.forEach(section => {
        section.style.height = '237px'; // 951px / 4
        section.style.minHeight = '237px';
        section.style.maxHeight = '237px';
        section.style.padding = '19px'; // 5mm
        section.style.borderBottom = '1px dashed #ccc';
        section.style.overflow = 'hidden';
        section.style.boxSizing = 'border-box';
    });

    // 移除最后一个section的底边框
    if (contentSections.length > 0) {
        contentSections[contentSections.length - 1].style.borderBottom = 'none';
    }

    tempContainer.appendChild(pageClone);

    return pageClone;
}







// 初始化页面
function init() {
    // 首先加载选项
    loadSelectOptions();

    // 为每个选择器添加事件监听
    for (let i = 1; i <= 4; i++) {
        const selector = document.getElementById(`section-${i}-selector`);
        if (selector) {
            selector.addEventListener('change', () => {
                saveConfig();  // 保存配置
                // 不自动重新生成，让用户手动点击生成按钮
            });
        }
    }

    // 为页面数量选择器添加事件监听
    const pageCountSelector = document.getElementById('page-count-selector');
    if (pageCountSelector) {
        pageCountSelector.addEventListener('change', () => {
            saveConfig();  // 保存配置
        });
    }

    // 加载保存的配置
    loadConfig();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', init);