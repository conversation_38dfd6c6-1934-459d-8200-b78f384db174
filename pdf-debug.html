<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF生成调试</title>
    <link rel="stylesheet" href="styles/a4.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .debug-controls {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .debug-controls button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .debug-controls button:hover {
            background: #45a049;
        }
        
        .debug-info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="debug-controls">
        <h2>PDF生成调试工具</h2>
        <button onclick="testSinglePage()">测试单页PDF</button>
        <button onclick="testMultiPage()">测试多页PDF</button>
        <button onclick="showPageInfo()">显示页面信息</button>
        <div id="debug-info" class="debug-info"></div>
    </div>

    <!-- 测试页面 -->
    <div class="print-page" id="test-page-1">
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">测试页面 - 第1页</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：2025-07-23</div>
                <div class="info-cell">姓名：测试</div>
                <div class="info-cell">计时：30分钟</div>
            </div>
        </div>
        
        <div class="content-area">
            <div class="content-section">
                <h3>第一部分：加减法</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <div>1. 25 + 37 = ______</div>
                    <div>2. 84 - 29 = ______</div>
                    <div>3. 56 + 18 = ______</div>
                    <div>4. 73 - 45 = ______</div>
                </div>
            </div>
            <div class="content-section">
                <h3>第二部分：乘除法</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <div>1. 6 × 7 = ______</div>
                    <div>2. 48 ÷ 8 = ______</div>
                    <div>3. 9 × 5 = ______</div>
                    <div>4. 63 ÷ 7 = ______</div>
                </div>
            </div>
            <div class="content-section">
                <h3>第三部分：应用题</h3>
                <div>
                    <p>1. 小明有25个苹果，小红有18个苹果，他们一共有多少个苹果？</p>
                    <p>答：_________________</p>
                </div>
            </div>
            <div class="content-section">
                <h3>第四部分：填空题</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                    <div>1. 5 + ( ) = 12</div>
                    <div>2. ( ) - 8 = 15</div>
                    <div>3. 6 × ( ) = 42</div>
                    <div>4. 36 ÷ ( ) = 9</div>
                </div>
            </div>
        </div>
    </div>

    <div class="print-page" id="test-page-2" style="margin-top: 20px;">
        <div class="page-header">
            <div class="header-top">
                <img src="/img/cal_paper.PNG" alt="页眉图片" class="header-image">
                <div class="header-title">测试页面 - 第2页</div>
            </div>
            <div class="header-info">
                <div class="info-cell">日期：2025-07-23</div>
                <div class="info-cell">姓名：测试</div>
                <div class="info-cell">计时：30分钟</div>
            </div>
        </div>
        
        <div class="content-area">
            <div class="content-section">
                <h3>第一部分：进阶加减法</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <div>1. 125 + 237 = ______</div>
                    <div>2. 384 - 129 = ______</div>
                    <div>3. 456 + 218 = ______</div>
                    <div>4. 573 - 245 = ______</div>
                </div>
            </div>
            <div class="content-section">
                <h3>第二部分：进阶乘除法</h3>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px;">
                    <div>1. 16 × 7 = ______</div>
                    <div>2. 144 ÷ 12 = ______</div>
                    <div>3. 19 × 5 = ______</div>
                    <div>4. 168 ÷ 14 = ______</div>
                </div>
            </div>
            <div class="content-section">
                <h3>第三部分：复杂应用题</h3>
                <div>
                    <p>1. 学校买了125本书，分给3个班级，每个班级分到多少本书？还剩多少本？</p>
                    <p>答：_________________</p>
                </div>
            </div>
            <div class="content-section">
                <h3>第四部分：计算题</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                    <div>1. 15 + 28 - 9 = ______</div>
                    <div>2. 6 × 8 ÷ 4 = ______</div>
                    <div>3. 45 - 18 + 27 = ______</div>
                    <div>4. 72 ÷ 9 × 5 = ______</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        function showPageInfo() {
            const pages = document.querySelectorAll('.print-page');
            log(`找到 ${pages.length} 个页面`);
            
            pages.forEach((page, index) => {
                const rect = page.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(page);
                log(`页面 ${index + 1}: ${rect.width.toFixed(1)}x${rect.height.toFixed(1)}px, CSS: ${computedStyle.width} x ${computedStyle.height}`);
            });
        }

        async function testSinglePage() {
            log('开始测试单页PDF...');
            const page = document.getElementById('test-page-1');
            
            if (!window.jspdf || !window.html2canvas) {
                log('❌ 缺少必要的库');
                return;
            }
            
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const canvas = await html2canvas(page, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    letterRendering: true,
                    backgroundColor: '#FFFFFF',
                    logging: true,
                    scrollX: 0,
                    scrollY: 0,
                    foreignObjectRendering: true
                });
                
                log(`Canvas尺寸: ${canvas.width}x${canvas.height}`);
                
                const imgData = canvas.toDataURL('image/jpeg', 0.95);
                pdf.addImage(imgData, 'JPEG', 0, 0, 210, 297);
                
                const blob = pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url);
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                
                log('✅ 单页PDF生成成功');
                
            } catch (error) {
                log('❌ 单页PDF生成失败: ' + error.message);
                console.error(error);
            }
        }

        async function testMultiPage() {
            log('开始测试多页PDF...');
            const pages = document.querySelectorAll('.print-page');
            
            if (!window.jspdf || !window.html2canvas) {
                log('❌ 缺少必要的库');
                return;
            }
            
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4'
                });

                for (let i = 0; i < pages.length; i++) {
                    log(`处理第 ${i + 1}/${pages.length} 页...`);
                    
                    if (i > 0) {
                        pdf.addPage();
                    }
                    
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    const canvas = await html2canvas(pages[i], {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        letterRendering: true,
                        backgroundColor: '#FFFFFF',
                        logging: false,
                        scrollX: 0,
                        scrollY: 0,
                        foreignObjectRendering: true
                    });
                    
                    log(`第 ${i + 1} 页Canvas: ${canvas.width}x${canvas.height}`);
                    
                    const imgData = canvas.toDataURL('image/jpeg', 0.95);
                    pdf.addImage(imgData, 'JPEG', 0, 0, 210, 297);
                    
                    log(`✅ 第 ${i + 1} 页已添加`);
                }

                const blob = pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url);
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                
                log(`✅ 多页PDF生成成功，共 ${pages.length} 页`);
                
            } catch (error) {
                log('❌ 多页PDF生成失败: ' + error.message);
                console.error(error);
            }
        }

        window.addEventListener('load', () => {
            log('页面加载完成');
            log('html2canvas: ' + (window.html2canvas ? '✅ 已加载' : '❌ 未加载'));
            log('jsPDF: ' + (window.jspdf ? '✅ 已加载' : '❌ 未加载'));
            showPageInfo();
        });
    </script>
</body>
</html>
