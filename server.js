const express = require('express');
const path = require('path');
const fs = require('fs');

// 尝试加载puppeteer，如果失败则提供替代方案
let puppeteer;
try {
    puppeteer = require('puppeteer');
} catch (error) {
    console.log('⚠️  Puppeteer未安装，将使用替代方案');
    puppeteer = null;
}

const app = express();
const PORT = 3000;

// 中间件
app.use(express.json());
app.use(express.static('.'));

// CORS支持
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// 生成PDF的API端点
app.post('/api/generate-pdf', async (req, res) => {
    let browser = null;
    
    try {
        console.log('开始生成PDF...');
        const { pages, filename = 'mathdrills.pdf' } = req.body;
        
        if (!pages || !Array.isArray(pages) || pages.length === 0) {
            return res.status(400).json({ error: '没有提供页面数据' });
        }
        
        console.log(`收到 ${pages.length} 页数据`);
        
        // 启动Puppeteer
        browser = await puppeteer.launch({
            headless: 'new',
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });
        
        const page = await browser.newPage();
        
        // 设置页面尺寸为A4
        await page.setViewport({
            width: 794,  // A4宽度 (210mm at 96dpi)
            height: 1123, // A4高度 (297mm at 96dpi)
            deviceScaleFactor: 2
        });
        
        // 创建完整的HTML文档
        const htmlContent = generateHTMLDocument(pages);
        
        // 设置页面内容
        await page.setContent(htmlContent, {
            waitUntil: ['networkidle0', 'domcontentloaded'],
            timeout: 30000
        });
        
        // 等待字体和图片加载
        await page.evaluateHandle('document.fonts.ready');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('开始生成PDF...');
        
        // 生成PDF
        const pdfBuffer = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '10mm',
                right: '10mm',
                bottom: '10mm',
                left: '10mm'
            },
            preferCSSPageSize: true
        });
        
        console.log(`PDF生成成功，大小: ${pdfBuffer.length} bytes`);
        
        // 设置响应头
        res.setHeader('Content-Type', 'application/pdf');

        // 对文件名进行URL编码以支持中文
        const encodedFilename = encodeURIComponent(filename);
        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Length', pdfBuffer.length);
        
        // 发送PDF
        res.send(pdfBuffer);
        
    } catch (error) {
        console.error('PDF生成失败:', error);
        res.status(500).json({ 
            error: 'PDF生成失败', 
            message: error.message 
        });
    } finally {
        if (browser) {
            await browser.close();
        }
    }
});

// 生成图片的API端点
app.post('/api/generate-images', async (req, res) => {
    let browser = null;
    
    try {
        console.log('开始生成图片...');
        const { pages } = req.body;
        
        if (!pages || !Array.isArray(pages) || pages.length === 0) {
            return res.status(400).json({ error: '没有提供页面数据' });
        }
        
        console.log(`收到 ${pages.length} 页数据`);
        
        // 启动Puppeteer
        browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const images = [];
        
        for (let i = 0; i < pages.length; i++) {
            console.log(`处理第 ${i + 1}/${pages.length} 页...`);
            
            const page = await browser.newPage();
            
            // 设置页面尺寸
            await page.setViewport({
                width: 794,
                height: 1123,
                deviceScaleFactor: 2
            });
            
            // 创建单页HTML
            const htmlContent = generateHTMLDocument([pages[i]]);
            
            await page.setContent(htmlContent, {
                waitUntil: ['networkidle0', 'domcontentloaded'],
                timeout: 30000
            });
            
            await page.evaluateHandle('document.fonts.ready');
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 截图
            const screenshot = await page.screenshot({
                type: 'png',
                fullPage: true,
                omitBackground: false
            });
            
            images.push({
                index: i + 1,
                data: screenshot.toString('base64'),
                filename: `mathdrills_page_${i + 1}.png`
            });
            
            await page.close();
        }
        
        console.log(`所有图片生成完成`);
        res.json({ images });
        
    } catch (error) {
        console.error('图片生成失败:', error);
        res.status(500).json({ 
            error: '图片生成失败', 
            message: error.message 
        });
    } finally {
        if (browser) {
            await browser.close();
        }
    }
});

// 生成完整的HTML文档
function generateHTMLDocument(pages) {
    const cssContent = fs.readFileSync('./styles/a4.css', 'utf8');
    
    const pagesHTML = pages.map((pageData, index) => pageData.html).join('');
    
    return `
    <!DOCTYPE html>
    <html lang="zh">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>数学练习题</title>
        <style>
            ${cssContent}
            
            /* Puppeteer优化样式 */
            body {
                margin: 0;
                padding: 0;
                background: white;
                font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            }
            
            .print-page {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 10mm;
                background: white;
                box-sizing: border-box;
                position: relative;
                overflow: hidden;
                page-break-after: always;
            }

            .print-page:last-child {
                page-break-after: auto;
            }
            
            @media print {
                .print-page {
                    page-break-after: always;
                }
                .print-page:last-child {
                    page-break-after: auto;
                }
            }
        </style>
    </head>
    <body>
        ${pagesHTML}
    </body>
    </html>
    `;
}

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`Puppeteer服务器运行在 http://localhost:${PORT}`);
    console.log('API端点:');
    console.log('  POST /api/generate-pdf - 生成PDF');
    console.log('  POST /api/generate-images - 生成图片');
    console.log('  GET  /api/health - 健康检查');
});

module.exports = app;
