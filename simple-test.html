<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单PDF测试</title>
    <link rel="stylesheet" href="styles/a4.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .test-controls button {
            padding: 10px 20px;
            margin: 5px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-controls">
        <h2>简单PDF测试</h2>
        <button onclick="testPDF()">生成PDF</button>
        <div id="status"></div>
    </div>

    <div id="pages-container">
        <div class="print-page">
            <div class="page-header">
                <div class="header-top">
                    <div class="header-title">测试页面 1</div>
                </div>
                <div class="header-info">
                    <div class="info-cell">日期：2025-07-23</div>
                    <div class="info-cell">姓名：测试</div>
                    <div class="info-cell">计时：30分钟</div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="content-section">
                    <h3>第一部分</h3>
                    <p>1 + 1 = ___</p>
                    <p>2 + 2 = ___</p>
                    <p>3 + 3 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第二部分</h3>
                    <p>4 + 4 = ___</p>
                    <p>5 + 5 = ___</p>
                    <p>6 + 6 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第三部分</h3>
                    <p>7 + 7 = ___</p>
                    <p>8 + 8 = ___</p>
                    <p>9 + 9 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第四部分</h3>
                    <p>10 + 10 = ___</p>
                    <p>11 + 11 = ___</p>
                    <p>12 + 12 = ___</p>
                </div>
            </div>
        </div>

        <div class="print-page">
            <div class="page-header">
                <div class="header-top">
                    <div class="header-title">测试页面 2</div>
                </div>
                <div class="header-info">
                    <div class="info-cell">日期：2025-07-23</div>
                    <div class="info-cell">姓名：测试</div>
                    <div class="info-cell">计时：30分钟</div>
                </div>
            </div>
            
            <div class="content-area">
                <div class="content-section">
                    <h3>第一部分</h3>
                    <p>13 + 13 = ___</p>
                    <p>14 + 14 = ___</p>
                    <p>15 + 15 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第二部分</h3>
                    <p>16 + 16 = ___</p>
                    <p>17 + 17 = ___</p>
                    <p>18 + 18 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第三部分</h3>
                    <p>19 + 19 = ___</p>
                    <p>20 + 20 = ___</p>
                    <p>21 + 21 = ___</p>
                </div>
                <div class="content-section">
                    <h3>第四部分</h3>
                    <p>22 + 22 = ___</p>
                    <p>23 + 23 = ___</p>
                    <p>24 + 24 = ___</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        async function testPDF() {
            log('开始测试PDF生成...');
            
            const printPages = document.querySelectorAll('.print-page');
            log(`找到 ${printPages.length} 个页面`);
            
            if (!window.html2pdf) {
                log('❌ html2pdf未加载');
                return;
            }
            
            try {
                // 使用与主项目相同的逻辑
                const tempContainer = document.createElement('div');
                tempContainer.style.position = 'absolute';
                tempContainer.style.left = '-9999px';
                tempContainer.style.top = '0';
                tempContainer.style.visibility = 'hidden';
                document.body.appendChild(tempContainer);

                const styles = document.querySelectorAll('style, link[rel="stylesheet"]');
                styles.forEach(style => {
                    const clonedStyle = style.cloneNode(true);
                    tempContainer.appendChild(clonedStyle);
                });

                const pagesWrapper = document.createElement('div');
                pagesWrapper.style.backgroundColor = '#FFFFFF';
                
                printPages.forEach((page, index) => {
                    const pageClone = page.cloneNode(true);
                    
                    pageClone.style.width = '210mm';
                    pageClone.style.height = '297mm';
                    pageClone.style.margin = '0';
                    pageClone.style.padding = '10mm';
                    pageClone.style.backgroundColor = '#FFFFFF';
                    pageClone.style.boxSizing = 'border-box';
                    pageClone.style.overflow = 'hidden';
                    pageClone.style.display = 'block';
                    pageClone.style.position = 'relative';
                    
                    if (index < printPages.length - 1) {
                        pageClone.style.pageBreakAfter = 'always';
                        pageClone.style.breakAfter = 'page';
                    }
                    
                    pagesWrapper.appendChild(pageClone);
                });

                tempContainer.appendChild(pagesWrapper);
                await new Promise(resolve => setTimeout(resolve, 1000));

                log('开始生成PDF...');

                const pdf = await html2pdf()
                    .set({
                        margin: [10, 10, 10, 10],
                        filename: `测试_${printPages.length}页.pdf`,
                        image: { 
                            type: 'jpeg', 
                            quality: 0.98
                        },
                        html2canvas: {
                            scale: 2,
                            useCORS: true,
                            letterRendering: true,
                            logging: false,
                            backgroundColor: '#FFFFFF',
                            allowTaint: true,
                            foreignObjectRendering: true
                        },
                        jsPDF: {
                            unit: 'mm',
                            format: 'a4',
                            orientation: 'portrait',
                            compress: true
                        },
                        pagebreak: { 
                            mode: ['avoid-all', 'css', 'legacy'],
                            before: '.print-page',
                            after: '.print-page'
                        }
                    })
                    .from(pagesWrapper)
                    .toPdf()
                    .get('pdf');

                document.body.removeChild(tempContainer);

                pdf.autoPrint();
                const blob = await pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url);
                setTimeout(() => URL.revokeObjectURL(url), 1000);

                log(`✅ PDF生成成功，共 ${printPages.length} 页`);

            } catch (error) {
                log('❌ PDF生成失败: ' + error.message);
                console.error(error);
            }
        }

        window.addEventListener('load', () => {
            log('页面加载完成');
            log('html2pdf: ' + (window.html2pdf ? '✅ 已加载' : '❌ 未加载'));
        });
    </script>
</body>
</html>
