#!/bin/bash

echo "🚀 启动数学练习题生成器服务"
echo "================================"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

echo "✅ npm版本: $(npm --version)"

# 安装依赖
echo ""
echo "📦 安装依赖包..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ 依赖安装失败"
    exit 1
fi

echo "✅ 依赖安装完成"

# 启动服务器
echo ""
echo "🌟 启动Puppeteer PDF生成服务..."
echo "   服务地址: http://localhost:3000"
echo "   前端页面: http://localhost:8000"
echo ""
echo "💡 使用说明:"
echo "   1. 保持此终端窗口打开"
echo "   2. 在另一个终端运行: python3 -m http.server 8000"
echo "   3. 打开浏览器访问: http://localhost:8000"
echo ""
echo "按 Ctrl+C 停止服务"
echo "================================"

node server.js
