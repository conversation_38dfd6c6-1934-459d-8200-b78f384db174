/* A4 纸张基础样式 */
:root {
    --a4-width: 210mm;
    --a4-height: 297mm;
    --page-margin: 10mm;
    --header-height: 25mm;
    --content-height: calc(var(--a4-height) - 2 * var(--page-margin) - var(--header-height));
    --section-height: calc(var(--content-height) / 4);
    --logo-size: 32px;
}

/* 打印页面容器 */
.print-page {
    width: var(--a4-width);
    height: var(--a4-height);
    margin: 0 auto;
    margin-left: 0;
    background: white;
    position: relative;
    box-sizing: border-box;
    padding: var(--page-margin);
    page-break-after: avoid;
}

/* 页眉样式 */
.page-header {
    height: var(--header-height);
    position: absolute;
    top: var(--page-margin);
    left: var(--page-margin);
    right: var(--page-margin);
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    gap: 2mm;
}

.header-top {
    display: flex;
    align-items: center;
    gap: 10mm;
}

.header-image {
    width: var(--logo-size);
    height: var(--logo-size);
}

.header-title {
    color: #333;
    font-size: 24px;
    font-weight: bold;
}

.header-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5mm;
    padding: 8px 0;
    border-top: 1px dashed #ccc;
    border-bottom: 1px dashed #ccc;
}

/* 内容区域 */
.content-area {
    position: absolute;
    top: calc(var(--page-margin) + var(--header-height));
    left: var(--page-margin);
    right: var(--page-margin);
    height: var(--content-height);
    display: flex;
    flex-direction: column;
}

.content-section {
    height: var(--section-height);
    min-height: var(--section-height);
    max-height: var(--section-height);
    padding: 5mm;
    border-bottom: 1px dashed #ccc;
    overflow: hidden;
    box-sizing: border-box;
}

.content-section:last-child {
    border-bottom: none;
}

/* 竖式布局 */
.vertical-calc {
    display: inline-block;
    text-align: right;
    margin: 0 10mm;
}

/* 应用题样式 */
.word-problem {
    margin-bottom: 8mm;
    line-height: 1.5;
}

/* 配置区页脚 */
.config-footer {
    text-align: center;
    padding: 10mm 0;
    color: #666;
    font-size: 12px;
}

/* 打印样式优化 */
@media print {
    body {
        margin: 0;
        padding: 0;
        background: none;
    }
    
    .print-page {
        margin: 0;
        padding: var(--page-margin);
        box-shadow: none;
        page-break-after: avoid;
    }
    
    .content-section {
        page-break-inside: avoid;
    }
}

/* 通用网格布局 */
.grid-2-cols { grid-template-columns: repeat(2, 1fr); }
.grid-3-cols { grid-template-columns: repeat(3, 1fr); }
.grid-4-cols { grid-template-columns: repeat(4, 1fr); }
.grid-5-cols { grid-template-columns: repeat(5, 1fr); }

/* 题目编号样式 */
.question-number {
    color: #666;
    font-size: 0.9em;
    margin-right: 3mm;
}

body {
    background: #f5f5f5;
    margin: 0;
    padding: var(--page-margin) 340px var(--page-margin) var(--page-margin);
    min-height: 100vh;
    display: flex;
    justify-content: flex-start;
}

/* 多页面容器 */
.pages-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 多页面打印样式 */
@media print {
    .pages-container {
        gap: 0;
    }

    .print-page {
        page-break-after: always;
    }

    .print-page:last-child {
        page-break-after: avoid;
    }
}