/* 配置区域布局 */
.container {
    display: flex;
    gap: 20px;
    padding: 20px;
}

/* 配置区样式 */
.config-area {
    position: fixed;
    top: var(--page-margin);  /* 使用与 A4 页面相同的边距 */
    right: 20px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 300px;
    z-index: 1000;
}

.config-section {
    margin-bottom: 20px;
}

.config-section h3 {
    color: #333;
    font-size: 16px;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #4CAF50;
}

.select-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.select-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.select-item label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.content-selector {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    color: #333;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.content-selector:hover {
    border-color: #4CAF50;
}

.content-selector:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.config-section button {
    width: 100%;
    padding: 10px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 8px;
}

.config-section button:hover {
    background-color: #45a049;
}

.config-section button:last-child {
    margin-bottom: 0;
}

#generate-button {
    background-color: #2196F3;
}

#generate-button:hover {
    background-color: #1976D2;
}

/* 按钮样式 */
button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

button:hover {
    background-color: #45a049;
}

/* 打印时隐藏配置区域 */
@media print {
    .config-area {
        display: none;
    }
}