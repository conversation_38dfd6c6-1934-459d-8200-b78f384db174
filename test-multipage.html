<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多页面PDF测试</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        .test-page {
            width: 210mm;
            height: 297mm;
            background: white;
            border: 1px solid #ccc;
            margin: 10px;
            padding: 20mm;
            box-sizing: border-box;
            page-break-after: always;
        }
        
        .test-page:last-child {
            page-break-after: auto;
        }
        
        .page-content {
            font-family: Arial, sans-serif;
            line-height: 1.6;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .math-problem {
            font-size: 18px;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #4CAF50;
        }
    </style>
</head>
<body>
    <h1>多页面PDF测试</h1>
    <button onclick="testMultiPagePDF()" style="padding: 10px 20px; font-size: 16px; margin: 20px;">生成多页面PDF</button>
    
    <div id="pages-container">
        <div class="test-page">
            <div class="page-content">
                <div class="page-title">数学练习 - 第1页</div>
                <div class="math-problem">1. 25 + 37 = ______</div>
                <div class="math-problem">2. 84 - 29 = ______</div>
                <div class="math-problem">3. 16 × 7 = ______</div>
                <div class="math-problem">4. 96 ÷ 8 = ______</div>
                <div class="math-problem">5. 45 + 28 = ______</div>
            </div>
        </div>
        
        <div class="test-page">
            <div class="page-content">
                <div class="page-title">数学练习 - 第2页</div>
                <div class="math-problem">1. 73 + 19 = ______</div>
                <div class="math-problem">2. 65 - 38 = ______</div>
                <div class="math-problem">3. 24 × 5 = ______</div>
                <div class="math-problem">4. 72 ÷ 9 = ______</div>
                <div class="math-problem">5. 56 + 47 = ______</div>
            </div>
        </div>
        
        <div class="test-page">
            <div class="page-content">
                <div class="page-title">数学练习 - 第3页</div>
                <div class="math-problem">1. 89 + 26 = ______</div>
                <div class="math-problem">2. 91 - 54 = ______</div>
                <div class="math-problem">3. 18 × 6 = ______</div>
                <div class="math-problem">4. 63 ÷ 7 = ______</div>
                <div class="math-problem">5. 34 + 59 = ______</div>
            </div>
        </div>
    </div>
    
    <div id="status" style="margin: 20px; padding: 10px; background: #f0f0f0;"></div>
    
    <script>
        function log(message) {
            const status = document.getElementById('status');
            status.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        async function testMultiPagePDF() {
            log('开始测试多页面PDF生成...');
            
            const pages = document.querySelectorAll('.test-page');
            log(`找到 ${pages.length} 个页面`);
            
            if (!window.jspdf || !window.html2canvas) {
                log('❌ 缺少必要的库');
                return;
            }
            
            try {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF({
                    orientation: 'portrait',
                    unit: 'mm',
                    format: 'a4',
                    compress: true
                });

                for (let i = 0; i < pages.length; i++) {
                    log(`处理第 ${i + 1}/${pages.length} 页...`);
                    
                    if (i > 0) {
                        pdf.addPage();
                    }
                    
                    // 等待渲染
                    await new Promise(resolve => setTimeout(resolve, 300));

                    const canvas = await html2canvas(pages[i], {
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        letterRendering: true,
                        backgroundColor: '#FFFFFF',
                        logging: false,
                        scrollX: 0,
                        scrollY: 0,
                        windowWidth: window.innerWidth,
                        windowHeight: window.innerHeight,
                        foreignObjectRendering: true,
                        removeContainer: true
                    });
                    
                    const imgData = canvas.toDataURL('image/jpeg', 0.95);

                    // 计算合适的尺寸
                    const pdfWidth = 210;
                    const pdfHeight = 297;
                    const imgAspectRatio = canvas.width / canvas.height;
                    const pdfAspectRatio = pdfWidth / pdfHeight;

                    let imgWidth, imgHeight;
                    if (imgAspectRatio > pdfAspectRatio) {
                        imgWidth = pdfWidth;
                        imgHeight = pdfWidth / imgAspectRatio;
                    } else {
                        imgHeight = pdfHeight;
                        imgWidth = pdfHeight * imgAspectRatio;
                    }

                    const x = (pdfWidth - imgWidth) / 2;
                    const y = (pdfHeight - imgHeight) / 2;

                    pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);

                    log(`Canvas: ${canvas.width}x${canvas.height}, PDF: ${imgWidth.toFixed(1)}x${imgHeight.toFixed(1)}mm`);
                    
                    log(`✅ 第 ${i + 1} 页已添加`);
                }

                log('生成PDF并打开...');
                pdf.autoPrint();
                const blob = pdf.output('blob');
                const url = URL.createObjectURL(blob);
                window.open(url);
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                
                log(`✅ 多页PDF生成成功，共 ${pages.length} 页`);
                
            } catch (error) {
                log('❌ PDF生成失败: ' + error.message);
                console.error(error);
            }
        }
        
        window.addEventListener('load', () => {
            log('页面加载完成');
            log('html2canvas: ' + (window.html2canvas ? '✅ 已加载' : '❌ 未加载'));
            log('jsPDF: ' + (window.jspdf ? '✅ 已加载' : '❌ 未加载'));
        });
    </script>
</body>
</html>
