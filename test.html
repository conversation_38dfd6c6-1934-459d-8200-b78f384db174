<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试</title>
</head>
<body>
    <h1>多页面功能测试</h1>
    <p>请打开主页面 <a href="/">index.html</a> 测试以下功能：</p>
    
    <h2>测试步骤：</h2>
    <ol>
        <li>选择第一部分内容（例如：计算-20道加减）</li>
        <li>选择第二部分内容（例如：计算-12道乘法口诀）</li>
        <li>选择页面数量（例如：3页）</li>
        <li>点击"生成练习题"按钮</li>
        <li>检查是否生成了3个页面</li>
        <li>每个页面是否都有不同的随机内容</li>
        <li>点击"打印练习题"按钮</li>
        <li>检查PDF是否包含所有页面</li>
    </ol>
    
    <h2>预期结果：</h2>
    <ul>
        <li>✅ 配置区显示页面数量选择器</li>
        <li>✅ 生成按钮和打印按钮分开</li>
        <li>✅ 能够生成指定数量的页面</li>
        <li>✅ 每页内容都是随机生成的</li>
        <li>✅ 每页标题显示页码</li>
        <li>✅ PDF包含所有页面，每页占满A4</li>
        <li>✅ 配置会自动保存和恢复</li>
    </ul>
</body>
</html>
